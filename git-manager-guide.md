# GitHub仓库管理脚本使用指南

## 概述

`git-manager.sh` 是一个功能完整的GitHub仓库管理脚本，提供了常用Git操作的简化命令接口。

## 安装和设置

1. 确保脚本有执行权限：
```bash
chmod +x git-manager.sh
```

2. 可选：将脚本添加到PATH中以便全局使用：
```bash
# 复制到用户bin目录
cp git-manager.sh ~/bin/git-manager
# 或者创建符号链接
ln -s $(pwd)/git-manager.sh ~/bin/git-manager
```

## 基本用法

```bash
./git-manager.sh [操作] [参数...]
```

## 功能分类

### 1. 基本操作

#### 查看状态
```bash
./git-manager.sh status
./git-manager.sh s          # 简写
```
显示仓库状态、未暂存文件、已暂存文件等信息。

#### 添加文件
```bash
./git-manager.sh add                    # 添加所有文件
./git-manager.sh add src/main.js        # 添加特定文件
./git-manager.sh add "*.js"             # 添加匹配模式的文件
```

#### 提交变更
```bash
./git-manager.sh commit "修复登录bug"
./git-manager.sh c "修复登录bug"         # 简写
./git-manager.sh commit                 # 交互式输入提交消息
```

#### 推送变更
```bash
./git-manager.sh push                   # 推送到当前分支
./git-manager.sh push main              # 推送到指定分支
./git-manager.sh p                      # 简写
```

#### 拉取变更
```bash
./git-manager.sh pull                   # 从当前分支拉取
./git-manager.sh pull main              # 从指定分支拉取
```

### 2. 快捷操作

#### 同步仓库
```bash
./git-manager.sh sync
```
执行完整的同步流程：拉取 → 添加 → 提交 → 推送

#### 快速提交推送
```bash
./git-manager.sh quick "完成新功能开发"
./git-manager.sh q "完成新功能开发"     # 简写
```
快速执行：添加所有文件 → 提交 → 推送

### 3. 分支操作

#### 查看分支
```bash
./git-manager.sh branch
./git-manager.sh b                      # 简写
```

#### 切换分支
```bash
./git-manager.sh checkout main
./git-manager.sh co feature/login       # 简写
```

#### 创建新分支
```bash
./git-manager.sh new-branch feature/payment
./git-manager.sh nb feature/payment     # 简写
```

#### 删除分支
```bash
./git-manager.sh delete-branch old-feature
./git-manager.sh db old-feature         # 简写
```

#### 合并分支
```bash
./git-manager.sh merge feature/login
```

### 4. 历史和日志

#### 查看提交历史
```bash
./git-manager.sh log                    # 显示最近10条
./git-manager.sh log 20                 # 显示最近20条
./git-manager.sh l 5                    # 简写，显示最近5条
```

#### 查看文件差异
```bash
./git-manager.sh diff                   # 查看所有差异
./git-manager.sh diff src/main.js       # 查看特定文件差异
```

#### 查看特定提交
```bash
./git-manager.sh show abc123            # 查看提交详情
```

### 5. 远程仓库操作

#### 查看远程仓库
```bash
./git-manager.sh remote
```

#### 克隆仓库
```bash
./git-manager.sh clone https://github.com/user/repo.git
./git-manager.sh clone https://github.com/user/repo.git my-project
```

#### 获取远程更新
```bash
./git-manager.sh fetch
```

### 6. 标签操作

#### 创建标签
```bash
./git-manager.sh tag v1.0.0
./git-manager.sh tag v1.0.0 "版本1.0.0发布"
```

#### 查看所有标签
```bash
./git-manager.sh tags
```

#### 推送标签
```bash
./git-manager.sh push-tags
```

### 7. 撤销操作

#### 撤销暂存区文件
```bash
./git-manager.sh reset                  # 撤销所有暂存文件
./git-manager.sh reset src/main.js      # 撤销特定文件
```

#### 撤销提交
```bash
./git-manager.sh revert abc123          # 撤销特定提交
```

#### 清理未跟踪文件
```bash
./git-manager.sh clean
```

### 8. 高级功能

#### 创建备份分支
```bash
./git-manager.sh backup
```
自动创建当前分支的备份，命名格式：`backup-分支名-时间戳`

#### 创建发布标签
```bash
./git-manager.sh release v2.0.0
```
创建标签并推送到远程仓库

## 实用示例

### 日常开发流程
```bash
# 1. 开始新功能开发
./git-manager.sh new-branch feature/user-profile

# 2. 开发过程中定期提交
./git-manager.sh quick "添加用户资料页面"
./git-manager.sh quick "完善用户资料编辑功能"

# 3. 功能完成后合并到主分支
./git-manager.sh checkout main
./git-manager.sh merge feature/user-profile

# 4. 推送主分支
./git-manager.sh push main
```

### 紧急修复流程
```bash
# 1. 创建备份
./git-manager.sh backup

# 2. 创建修复分支
./git-manager.sh new-branch hotfix/critical-bug

# 3. 修复并快速部署
./git-manager.sh quick "修复关键安全漏洞"

# 4. 合并到主分支
./git-manager.sh checkout main
./git-manager.sh merge hotfix/critical-bug
./git-manager.sh push main
```

### 版本发布流程
```bash
# 1. 确保代码最新
./git-manager.sh sync

# 2. 创建发布标签
./git-manager.sh release v1.2.0

# 3. 查看发布历史
./git-manager.sh tags
```

## 错误处理

脚本包含完善的错误处理机制：
- 自动检查是否在Git仓库中
- 操作失败时显示错误信息并退出
- 提供彩色输出便于识别状态

## 自定义配置

可以通过修改脚本顶部的颜色定义来自定义输出样式：
```bash
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
# ... 其他颜色定义
```

## 注意事项

1. 确保已配置Git用户信息：
```bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

2. 对于需要认证的操作，确保已设置SSH密钥或访问令牌

3. 使用 `sync` 和 `quick` 命令时要谨慎，它们会自动提交所有变更

4. 删除分支前确保已合并重要变更

## 获取帮助

随时使用以下命令查看帮助：
```bash
./git-manager.sh help
./git-manager.sh h
./git-manager.sh          # 无参数时显示帮助
```
