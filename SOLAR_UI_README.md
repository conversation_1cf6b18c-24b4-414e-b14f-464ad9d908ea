# 🌞 VPP-AI 光伏发电预测 UI 系统

## 📋 系统概述

VPP-AI光伏发电预测UI系统是一个基于Python的交互式Web应用，集成了谷歌地图、多组光伏配置、NASA POWER API数据获取和AI预测模型，为用户提供直观、专业的光伏发电预测服务。

## ✨ 核心功能

### 🗺️ 智能位置选择
- **谷歌地图集成**: 可视化选择项目位置
- **地址搜索**: 支持地址搜索和自动定位
- **坐标显示**: 实时显示经纬度坐标
- **快速定位**: 一键定位到常用城市

### ⚡ 多组光伏配置
- **灵活配置**: 支持多组光伏板独立配置
- **详细参数**: 面积、效率、朝向、倾角等完整参数
- **实时预览**: 系统概览实时更新
- **参数验证**: 智能参数范围检查

### 📈 历史发电分析
- **NASA POWER数据**: 获取高精度历史天气数据
- **发电模拟**: 基于天气数据计算历史发电功率
- **多维可视化**: 功率、辐照度、温度等多维度图表
- **数据导出**: 支持CSV格式数据导出

### 🔮 AI发电预测
- **多模型预测**: LSTM、Prophet、XGBoost等多种AI模型
- **置信区间**: 提供预测不确定性分析
- **未来预测**: 支持24小时到7天的预测时长
- **实时天气**: 集成天气预测数据

### 📊 丰富的数据可视化
- **交互式图表**: 基于Plotly的高质量图表
- **多视图切换**: 功率、辐照度、天气、对比等多个视图
- **数据表格**: 详细数据表格展示
- **统计摘要**: 关键指标统计分析

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd VPP-AI

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

确保 `config.yaml` 中包含必要的配置：

```yaml
nasa_power:
  base_url: "https://power.larc.nasa.gov/api/temporal/hourly/point"
  community: "RE"
  parameters: ["ALLSKY_SFC_SW_DWN", "T2M", "RH2M", "WS10M"]
  cache_enabled: true
  cache_ttl: 3600

solar:
  latitude: 39.9042
  longitude: 116.4074
```

### 3. 启动系统

#### 方式一：Streamlit UI (推荐)
```bash
python run_solar_ui.py
# 或
python run_solar_ui.py --mode streamlit
```

#### 方式二：Flask Web UI
```bash
python run_solar_ui.py --mode flask
```

#### 方式三：完整系统
```bash
python run_solar_ui.py --mode both
```

### 4. 访问系统

- **Streamlit UI**: http://localhost:8501
- **Flask Web UI**: http://localhost:5000

## 📖 使用指南

### 步骤1：选择项目位置

1. 在地图上点击选择位置，或拖拽标记
2. 使用地址搜索框搜索具体地址
3. 点击"定位我的位置"获取当前位置
4. 手动输入精确的经纬度坐标

### 步骤2：配置光伏系统

1. 点击"添加光伏组"配置新的光伏组
2. 设置光伏板参数：
   - **面积**: 光伏板总面积 (m²)
   - **效率**: 光伏板转换效率 (%)
   - **额定功率**: 系统额定功率 (kW)
   - **朝向角度**: 光伏板朝向 (0°=北, 180°=南)
   - **倾斜角度**: 光伏板倾斜角度 (0°=水平, 90°=垂直)
   - **遮挡系数**: 考虑遮挡影响 (0-1)
   - **污染系数**: 考虑污染影响 (0-1)

3. 可以添加多组不同配置的光伏系统
4. 查看系统概览了解总体配置

### 步骤3：历史发电分析

1. 切换到"历史分析"标签页
2. 设置分析参数：
   - **开始日期**: 历史分析开始时间
   - **结束日期**: 历史分析结束时间
   - **数据精度**: 小时级或日级数据
   - **光伏组选择**: 选择要分析的光伏组

3. 点击"开始历史分析"
4. 查看分析结果：
   - 发电功率时间序列图
   - 太阳辐照度变化图
   - 环境温度变化图
   - 统计摘要信息

### 步骤4：发电功率预测

1. 切换到"发电预测"标签页
2. 设置预测参数：
   - **预测时长**: 24小时、48小时、72小时或7天
   - **光伏组选择**: 选择要预测的光伏组
   - **预测模型**: LSTM、Prophet、XGBoost或集成模型
   - **置信区间**: 设置预测置信水平

3. 点击"开始预测"
4. 查看预测结果：
   - 发电功率预测曲线
   - 置信区间显示
   - 天气条件预测
   - 预测统计摘要

## 🛠️ 技术架构

### 前端技术栈
- **Streamlit**: 主要UI框架
- **Plotly**: 交互式图表库
- **Folium**: 地图组件
- **Pandas**: 数据处理
- **NumPy**: 数值计算

### 后端技术栈
- **Flask**: Web API框架
- **NASA POWER API**: 天气数据源
- **TensorFlow/PyTorch**: 深度学习模型
- **Prophet**: 时间序列预测
- **XGBoost**: 梯度提升模型

### 数据流程
1. **位置选择** → 获取地理坐标
2. **光伏配置** → 创建光伏板模型
3. **天气数据** → NASA POWER API获取
4. **功率计算** → 物理模型计算
5. **AI预测** → 机器学习模型预测
6. **结果展示** → 可视化图表展示

## 📊 API接口

### 历史分析接口
```http
POST /api/historical-analysis
Content-Type: application/json

{
  "location": {"lat": 39.9042, "lng": 116.4074},
  "solar_groups": [...],
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "precision": "hourly"
}
```

### 发电预测接口
```http
POST /api/power-prediction
Content-Type: application/json

{
  "location": {"lat": 39.9042, "lng": 116.4074},
  "solar_groups": [...],
  "forecast_hours": 24,
  "model_type": "ensemble"
}
```

## 🔧 配置说明

### NASA POWER API配置
```yaml
nasa_power:
  base_url: "https://power.larc.nasa.gov/api/temporal/hourly/point"
  community: "RE"  # 可再生能源社区
  parameters:
    - "ALLSKY_SFC_SW_DWN"  # 全球水平辐照度
    - "T2M"                # 2米高度温度
    - "RH2M"               # 2米高度相对湿度
    - "WS10M"              # 10米高度风速
  cache_enabled: true
  cache_ttl: 3600
  max_retries: 3
  timeout: 30
```

### 光伏系统默认配置
```yaml
solar:
  latitude: 39.9042      # 默认纬度
  longitude: 116.4074    # 默认经度
  default_efficiency: 0.2 # 默认转换效率
  default_tilt: 30       # 默认倾斜角度
  default_orientation: 180 # 默认朝向(南)
```

## 🚨 注意事项

1. **API限制**: NASA POWER API有请求频率限制，建议启用缓存
2. **数据延迟**: NASA POWER数据可能有5-7天延迟
3. **预测精度**: 预测结果仅供参考，实际发电量受多种因素影响
4. **浏览器兼容**: 建议使用Chrome、Firefox等现代浏览器
5. **网络要求**: 需要稳定的网络连接访问外部API

## 🐛 故障排除

### 常见问题

**Q: 无法获取天气数据**
A: 检查网络连接和NASA POWER API状态，确认坐标范围有效

**Q: 地图无法显示**
A: 检查网络连接，确保可以访问地图服务

**Q: 预测结果异常**
A: 检查光伏配置参数是否合理，确认历史数据充足

**Q: 页面加载缓慢**
A: 启用数据缓存，减少API请求频率

### 日志查看
```bash
# 查看应用日志
tail -f logs/vpp_ai.log

# 查看错误日志
grep ERROR logs/vpp_ai.log
```

## 📞 技术支持

如有问题或建议，请联系开发团队或提交Issue。

---

**VPP-AI团队** | 让光伏预测更智能 🌞
