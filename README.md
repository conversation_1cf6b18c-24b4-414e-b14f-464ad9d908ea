# 🌞 VPP-AI 新能源微网决策系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)
[![API Status](https://img.shields.io/badge/API-Running-brightgreen.svg)](http://localhost:8000)

> 基于人工智能的光伏发电预测和优化决策系统

## 📋 项目概述

VPP-AI（Virtual Power Plant with AI）是一个基于人工智能的新能源微网智能决策系统，专为光伏储能项目的经济收益最大化而设计。系统集成了NASA POWER天气数据API和多种机器学习模型，能够为任意地理位置提供高精度的光伏发电预测，实现经济收益最大化的数字孪生系统。

**🎉 当前状态**: 光伏发电预测模块已完成开发并可运行！

## 核心功能

### 🌞 光伏发电预测模块 ✅ **已完成**
- ✅ **NASA POWER API集成**: 全球天气数据获取，支持任意地理位置
- ✅ **高精度太阳位置计算**: 基于天文算法的太阳高度角和方位角计算
- ✅ **多种AI预测模型**: LSTM、Prophet、XGBoost等机器学习模型
- ✅ **光伏发电建模**: 考虑温度、效率、遮挡、倾斜角等因素
- ✅ **置信区间分析**: 提供预测不确定性评估
- ✅ **Web API服务**: FastAPI框架的现代化API接口
- ✅ **数据可视化**: 实时图表和趋势分析

### 🔋 储能管理模块 🚧 **规划中**
- 智能充放电策略优化
- 电池状态监控和健康管理
- 多种储能技术支持（锂电池、铅酸电池等）
- 考虑电池寿命和效率的优化算法

### ⚡ 负荷预测模块 🚧 **规划中**
- 基于历史用电数据的负荷预测
- 支持多种负荷类型（空调、照明、生产设备、电动汽车充电等）
- 人工输入用电计划的集成
- 负荷灵活性分析和优化

### 💰 电网交易模块 🚧 **规划中**
- 分时电价分析和预测
- 上网售电策略优化
- 电力市场交易决策支持
- 收益最大化算法

### 🏭 数字孪生仿真模块 🚧 **规划中**
- 设备数字化建模
- 实时系统仿真
- 多场景分析和对比
- 设备性能监控和诊断

### 🧠 决策优化引擎 🚧 **规划中**
- 多目标优化算法
- 实时决策支持
- 风险评估和管理
- 经济收益最大化

## 技术架构

### 后端技术栈
- **框架**: FastAPI + SQLAlchemy + Pydantic
- **数据库**: PostgreSQL + Redis + MongoDB
- **AI/ML**: TensorFlow + PyTorch + Scikit-learn
- **优化**: CVXPY + PuLP
- **异步**: Celery + Redis

### 前端技术栈
- **框架**: React + TypeScript
- **可视化**: Plotly + D3.js + ECharts
- **UI组件**: Ant Design
- **状态管理**: Redux Toolkit

### 部署架构
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes（可选）
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

## 项目结构

```
VPP-AI/
├── src/                    # 源代码
│   ├── core/              # 核心模块
│   │   ├── config.py      # 配置管理
│   │   ├── database.py    # 数据库管理
│   │   ├── logger.py      # 日志管理
│   │   └── exceptions.py  # 异常处理
│   ├── models/            # 数据模型
│   │   ├── solar.py       # 光伏模型
│   │   ├── storage.py     # 储能模型
│   │   ├── load.py        # 负荷模型
│   │   ├── grid.py        # 电网模型
│   │   └── weather.py     # 天气模型
│   ├── services/          # 业务服务
│   │   ├── prediction/    # 预测服务
│   │   ├── optimization/  # 优化服务
│   │   ├── simulation/    # 仿真服务
│   │   └── monitoring/    # 监控服务
│   ├── api/               # API接口
│   │   ├── routes/        # 路由定义
│   │   ├── schemas/       # 数据模式
│   │   └── middleware/    # 中间件
│   └── utils/             # 工具函数
├── tests/                 # 测试代码
│   ├── unit/             # 单元测试
│   └── integration/      # 集成测试
├── data/                 # 数据目录
│   ├── raw/              # 原始数据
│   ├── processed/        # 处理后数据
│   └── models/           # 训练模型
├── docs/                 # 文档
├── config/               # 配置文件
├── logs/                 # 日志文件
├── main.py              # 主入口
├── config.yaml          # 主配置文件
└── requirements.txt     # 依赖包
```

## 开发计划

### 第一阶段：基础架构 ✅ **已完成**
- [x] 项目结构搭建
- [x] 核心模块开发（配置、数据库、日志、异常处理）
- [x] 数据模型设计（光伏、储能、负荷、电网、天气）
- [x] 基础工具和依赖配置

### 第二阶段：光伏预测模块 ✅ **已完成**
- [x] NASA POWER API集成
- [x] 太阳位置计算算法
- [x] 光伏发电功率计算
- [x] AI预测模型框架（LSTM、Prophet、XGBoost）
- [x] Web API服务开发
- [x] 数据可视化功能
- [x] 完整的测试套件

### 第三阶段：数据层开发 🚧 **规划中**
- [ ] 数据库表结构创建
- [ ] 数据访问层（DAO）开发
- [ ] 数据验证和清洗
- [ ] 时序数据存储优化

### 第四阶段：扩展预测模块 🚧 **规划中**
- [ ] 负荷预测模型
- [ ] 储能状态预测
- [ ] 电价预测模型
- [ ] 模型训练和评估框架优化

### 第四阶段：优化引擎
- [ ] 储能充放电优化
- [ ] 电网交易策略优化
- [ ] 多目标优化算法
- [ ] 实时决策引擎

### 第五阶段：数字孪生
- [ ] 设备建模框架
- [ ] 实时仿真引擎
- [ ] 场景分析工具
- [ ] 性能监控系统

### 第六阶段：API和界面
- [ ] RESTful API开发
- [ ] Web管理界面
- [ ] 移动端应用
- [ ] 数据可视化

### 第七阶段：部署和运维
- [ ] Docker容器化
- [ ] CI/CD流水线
- [ ] 监控和告警
- [ ] 性能优化

## 应用场景

- **工业园区**: 大型工业用电负荷的光伏储能优化
- **商业综合体**: 商业建筑的能源管理和成本优化
- **住宅小区**: 居民用电的光伏储能系统优化
- **数据中心**: 高能耗设施的绿色能源解决方案

## 🚀 快速开始

### 环境要求
- Python 3.8+
- pip 或 conda 包管理器
- Git

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/wsd07/VPP-AI.git
cd VPP-AI
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **运行测试**
```bash
# 核心功能测试
python test_weather_only.py

# 完整演示
python demo_core_features.py
```

5. **启动API服务**
```bash
python simple_api.py
```

6. **访问服务**
- 主页: http://localhost:8000
- API文档: http://localhost:8000/docs
- 系统状态: http://localhost:8000/api/status

### 🧪 测试结果

```
============================================================
📊 测试总结
============================================================
基本配置: ✅ 通过
天气工具: ✅ 通过
NASA POWER基本功能: ✅ 通过
数据处理: ✅ 通过
光伏计算: ✅ 通过

总计: 5/5 测试通过
🎉 所有核心功能测试通过！
```

### 📊 API使用示例

```python
import requests

# 光伏发电预测
response = requests.post('http://localhost:8000/api/solar/predict', json={
    "latitude": 39.9042,
    "longitude": 116.4074,
    "hours": 24
})

predictions = response.json()
print(f"峰值功率: {max(p['predicted_power'] for p in predictions):.1f}kW")
```

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License

## 联系方式

项目开发者：新能源从业者
开发时间：2025年6月

---

**注意**: 这是一个正在开发中的项目，功能将逐步完善。
