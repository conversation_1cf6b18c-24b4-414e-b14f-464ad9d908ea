"""
光伏发电预测服务
================

集成多种预测模型，提供光伏发电功率预测功能。
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import os
import json
from dataclasses import dataclass, asdict

from .nasa_power_service import NASAPowerService, WeatherData
from .prediction_models import LSTMModel, ProphetModel, XGBoostModel, BasePredictionModel
from src.core.config import config
from src.core.logger import get_logger
from src.core.exceptions import VPPAIException
from src.database.dao import SolarDAO
from src.models.solar import SolarPanel, SolarData, SolarForecast

logger = get_logger(__name__)


@dataclass
class PredictionResult:
    """预测结果数据结构"""
    timestamp: datetime
    predicted_power: float  # 预测功率 (kW)
    predicted_energy: float  # 预测发电量 (kWh)
    confidence_lower: float  # 置信区间下界
    confidence_upper: float  # 置信区间上界
    model_name: str  # 使用的模型名称
    weather_conditions: Dict[str, float]  # 天气条件


class SolarPredictionService:
    """光伏发电预测服务"""
    
    def __init__(self):
        """初始化预测服务"""
        self.nasa_service = NASAPowerService()
        self.solar_dao = SolarDAO()
        
        # 加载配置
        self.prediction_config = config.get('solar.prediction', {})
        self.models_config = self.prediction_config.get('models', [])
        self.forecast_horizon = self.prediction_config.get('forecast_horizon', 24)
        self.confidence_level = self.prediction_config.get('confidence_level', 0.95)
        
        # 初始化模型
        self.models = {}
        self._initialize_models()
        
        # 模型权重
        self.model_weights = {}
        for model_config in self.models_config:
            if model_config.get('enabled', True):
                self.model_weights[model_config['name']] = model_config.get('weight', 1.0)
        
        # 标准化权重
        total_weight = sum(self.model_weights.values())
        if total_weight > 0:
            self.model_weights = {k: v/total_weight for k, v in self.model_weights.items()}
        
        logger.info(f"光伏预测服务初始化完成，启用模型: {list(self.model_weights.keys())}")
    
    def _initialize_models(self) -> None:
        """初始化预测模型"""
        for model_config in self.models_config:
            if not model_config.get('enabled', True):
                continue
                
            model_name = model_config['name']
            model_params = model_config.get('params', {})
            
            try:
                if model_name == 'lstm':
                    self.models[model_name] = LSTMModel(model_params)
                elif model_name == 'prophet':
                    self.models[model_name] = ProphetModel(model_params)
                elif model_name == 'xgboost':
                    self.models[model_name] = XGBoostModel(model_params)
                else:
                    logger.warning(f"未知的模型类型: {model_name}")
                    continue
                
                # 尝试加载已训练的模型
                model_path = f"data/models/{model_name}_solar_model.pkl"
                if os.path.exists(model_path):
                    self.models[model_name].load_model(model_path)
                    logger.info(f"已加载预训练的{model_name}模型")
                
            except Exception as e:
                logger.error(f"初始化{model_name}模型失败: {e}")
    
    def calculate_solar_power(self, weather_data: WeatherData, panel: SolarPanel) -> float:
        """
        根据天气数据和光伏板参数计算理论发电功率
        
        Args:
            weather_data: 天气数据
            panel: 光伏板参数
            
        Returns:
            理论发电功率 (kW)
        """
        # 基本参数
        ghi = weather_data.ghi  # 全球水平辐照度 (W/m²)
        temperature = weather_data.temperature  # 温度 (°C)
        
        # 光伏板参数 - 添加空值检查和默认值
        area = panel.area or 0  # 面积 (m²)
        efficiency = (panel.efficiency or 0) / 100 if hasattr(panel, 'efficiency') and panel.efficiency else 0.2  # 转换效率
        temp_coeff = getattr(panel, 'temperature_coefficient', -0.004)  # 温度系数
        shading_factor = getattr(panel, 'shading_factor', 1.0)  # 遮挡系数
        soiling_factor = getattr(panel, 'soiling_factor', 0.95)  # 污染系数
        
        # 温度修正
        # 标准测试条件下的温度为25°C
        temp_correction = 1 + temp_coeff * (temperature - 25)
        
        # 计算发电功率
        power = (ghi * area * efficiency * temp_correction * 
                shading_factor * soiling_factor) / 1000  # 转换为kW
        
        # 考虑逆变器效率
        inverter_efficiency = getattr(panel, 'inverter_efficiency', 0.95)
        power *= inverter_efficiency
        
        return max(0, power)  # 确保非负值
    
    def prepare_training_data(self, panel_id: int, days: int = 30) -> pd.DataFrame:
        """
        准备训练数据
        
        Args:
            panel_id: 光伏板ID
            days: 历史数据天数
            
        Returns:
            训练数据DataFrame
        """
        # 获取历史发电数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        solar_data = self.solar_dao.get_solar_data(panel_id, start_date, end_date)
        
        if not solar_data:
            raise VPPAIException(f"光伏板 {panel_id} 没有足够的历史数据")
        
        # 转换为DataFrame
        data_records = []
        for record in solar_data:
            data_records.append({
                'timestamp': record.timestamp,
                'power_output': record.power_output,
                'irradiance': record.irradiance,
                'panel_temperature': record.panel_temperature,
                'ambient_temperature': record.ambient_temperature,
                'wind_speed': record.wind_speed,
                'humidity': record.humidity
            })
        
        df = pd.DataFrame(data_records)
        df.set_index('timestamp', inplace=True)
        
        # 获取对应的天气数据
        panel = self.solar_dao.get_by_id(panel_id)
        if panel:
            weather_data = self.nasa_service.get_historical_data(
                panel.latitude, panel.longitude, start_date, end_date
            )
            
            # 合并天气数据
            weather_df = self.nasa_service.to_dataframe(weather_data)
            df = df.join(weather_df, how='left')
        
        # 数据清洗
        df = df.dropna()
        df = self._clean_data(df)
        
        return df
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据清洗和预处理"""
        # 移除异常值
        outlier_threshold = self.prediction_config.get('outlier_threshold', 3.0)
        
        for column in ['power_output', 'ghi', 'temperature']:
            if column in df.columns:
                mean = df[column].mean()
                std = df[column].std()
                df = df[np.abs(df[column] - mean) <= outlier_threshold * std]
        
        # 数据平滑
        smoothing_window = self.prediction_config.get('smoothing_window', 3)
        if smoothing_window > 1:
            for column in ['power_output', 'ghi']:
                if column in df.columns:
                    df[column] = df[column].rolling(window=smoothing_window, center=True).mean()
        
        return df.dropna()
    
    def train_models(self, panel_id: int, retrain: bool = False) -> Dict[str, Any]:
        """
        训练预测模型
        
        Args:
            panel_id: 光伏板ID
            retrain: 是否强制重新训练
            
        Returns:
            训练结果
        """
        logger.info(f"开始训练光伏板 {panel_id} 的预测模型")
        
        # 检查是否需要重新训练
        if not retrain:
            last_train_date = self._get_last_train_date(panel_id)
            if last_train_date:
                days_since_train = (datetime.now() - last_train_date).days
                retrain_interval = self.prediction_config.get('model_retrain_days', 30)
                if days_since_train < retrain_interval:
                    logger.info(f"模型训练时间未到，跳过训练")
                    return {'status': 'skipped', 'reason': 'not_due'}
        
        # 准备训练数据
        training_data = self.prepare_training_data(panel_id)
        
        if len(training_data) < self.prediction_config.get('min_data_points', 168):
            raise VPPAIException("训练数据不足")
        
        # 训练各个模型
        training_results = {}
        
        for model_name, model in self.models.items():
            try:
                logger.info(f"训练 {model_name} 模型")
                model.train(training_data, 'power_output')
                
                # 保存模型
                model_path = f"data/models/{model_name}_solar_model_{panel_id}.pkl"
                os.makedirs(os.path.dirname(model_path), exist_ok=True)
                model.save_model(model_path)
                
                # 评估模型
                test_size = min(len(training_data) // 4, 168)  # 最多一周的测试数据
                test_data = training_data.tail(test_size)
                
                if len(test_data) > 24:
                    predictions, _ = model.predict(test_data.head(-24), 24)
                    actual = test_data.tail(24)['power_output'].values
                    
                    if len(predictions) == len(actual):
                        metrics = model.evaluate(actual, predictions)
                        training_results[model_name] = {
                            'status': 'success',
                            'metrics': metrics
                        }
                    else:
                        training_results[model_name] = {
                            'status': 'success',
                            'metrics': None
                        }
                else:
                    training_results[model_name] = {
                        'status': 'success',
                        'metrics': None
                    }
                
            except Exception as e:
                logger.error(f"训练 {model_name} 模型失败: {e}")
                training_results[model_name] = {
                    'status': 'failed',
                    'error': str(e)
                }
        
        # 记录训练时间
        self._save_train_date(panel_id, datetime.now())
        
        logger.info(f"模型训练完成: {training_results}")
        return training_results
    
    def predict_solar_power(self, panel_id: int, latitude: float = None, 
                          longitude: float = None, horizon: int = None) -> List[PredictionResult]:
        """
        预测光伏发电功率
        
        Args:
            panel_id: 光伏板ID
            latitude: 纬度（可选，默认使用光伏板配置）
            longitude: 经度（可选，默认使用光伏板配置）
            horizon: 预测时长（小时，可选）
            
        Returns:
            预测结果列表
        """
        logger.info(f"开始预测光伏板 {panel_id} 的发电功率")
        
        # 获取光伏板信息
        panel = self.solar_dao.get_by_id(panel_id)
        if not panel:
            raise VPPAIException(f"光伏板 {panel_id} 不存在")
        
        # 使用默认坐标
        lat = latitude or panel.latitude or config.get('solar.latitude')
        lon = longitude or panel.longitude or config.get('solar.longitude')
        
        if not lat or not lon:
            raise VPPAIException("缺少地理坐标信息")
        
        # 预测时长
        horizon = horizon or self.forecast_horizon
        
        # 获取天气预测数据
        weather_forecast = self.nasa_service.get_forecast_data(lat, lon, horizon)
        
        if not weather_forecast:
            raise VPPAIException("无法获取天气预测数据")
        
        # 准备历史数据用于模型预测
        historical_data = self.prepare_training_data(panel_id, days=7)
        
        # 使用各个模型进行预测
        model_predictions = {}
        
        for model_name, model in self.models.items():
            if not model.is_trained:
                logger.warning(f"{model_name} 模型未训练，跳过预测")
                continue
            
            try:
                predictions, confidence = model.predict(historical_data, horizon)
                model_predictions[model_name] = {
                    'predictions': predictions,
                    'confidence': confidence
                }
            except Exception as e:
                logger.error(f"{model_name} 模型预测失败: {e}")
        
        if not model_predictions:
            raise VPPAIException("没有可用的预测模型")
        
        # 集成预测结果
        results = []
        base_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        
        for i in range(horizon):
            timestamp = base_time + timedelta(hours=i+1)
            weather = weather_forecast[i] if i < len(weather_forecast) else weather_forecast[-1]
            
            # 加权平均预测结果
            weighted_prediction = 0
            weighted_confidence = 0
            total_weight = 0
            
            for model_name, pred_data in model_predictions.items():
                weight = self.model_weights.get(model_name, 0)
                if weight > 0 and i < len(pred_data['predictions']):
                    weighted_prediction += pred_data['predictions'][i] * weight
                    weighted_confidence += pred_data['confidence'][i] * weight
                    total_weight += weight
            
            if total_weight > 0:
                weighted_prediction /= total_weight
                weighted_confidence /= total_weight
            
            # 计算置信区间
            confidence_lower = max(0, weighted_prediction - weighted_confidence)
            confidence_upper = weighted_prediction + weighted_confidence
            
            # 计算预测发电量（功率 × 1小时）
            predicted_energy = weighted_prediction * 1.0  # kWh
            
            result = PredictionResult(
                timestamp=timestamp,
                predicted_power=weighted_prediction,
                predicted_energy=predicted_energy,
                confidence_lower=confidence_lower,
                confidence_upper=confidence_upper,
                model_name='ensemble',
                weather_conditions={
                    'ghi': weather.ghi,
                    'temperature': weather.temperature,
                    'humidity': weather.humidity,
                    'wind_speed': weather.wind_speed,
                    'cloud_cover': weather.cloud_cover
                }
            )
            
            results.append(result)
        
        logger.info(f"预测完成，生成 {len(results)} 个预测点")
        return results
    
    def _get_last_train_date(self, panel_id: int) -> Optional[datetime]:
        """获取最后训练日期"""
        # 这里可以从数据库或文件中读取
        # 简化实现：检查模型文件的修改时间
        model_path = f"data/models/lstm_solar_model_{panel_id}.pkl"
        if os.path.exists(model_path):
            timestamp = os.path.getmtime(model_path)
            return datetime.fromtimestamp(timestamp)
        return None
    
    def _save_train_date(self, panel_id: int, train_date: datetime) -> None:
        """保存训练日期"""
        # 简化实现：通过文件时间戳记录
        train_info_path = f"data/models/train_info_{panel_id}.json"
        os.makedirs(os.path.dirname(train_info_path), exist_ok=True)
        
        with open(train_info_path, 'w') as f:
            json.dump({
                'panel_id': panel_id,
                'last_train_date': train_date.isoformat()
            }, f)
    
    def save_predictions(self, panel_id: int, predictions: List[PredictionResult]) -> None:
        """保存预测结果到数据库"""
        for pred in predictions:
            forecast_data = {
                'panel_id': panel_id,
                'forecast_time': datetime.now(),
                'target_time': pred.timestamp,
                'predicted_power': pred.predicted_power,
                'predicted_energy': pred.predicted_energy,
                'confidence_level': self.confidence_level,
                'power_lower_bound': pred.confidence_lower,
                'power_upper_bound': pred.confidence_upper,
                'model_name': pred.model_name,
                'weather_forecast_id': None,  # 可以关联天气预测ID
                'predicted_irradiance': pred.weather_conditions.get('ghi', 0)
            }
            
            try:
                # 这里应该使用SolarForecastDAO保存数据
                # 简化实现：直接记录日志
                logger.info(f"预测结果: {pred.timestamp} - {pred.predicted_power:.2f}kW")
            except Exception as e:
                logger.error(f"保存预测结果失败: {e}")
