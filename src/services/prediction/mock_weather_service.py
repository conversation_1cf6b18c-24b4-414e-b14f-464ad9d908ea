#!/usr/bin/env python3
"""
🌤️ 模拟天气数据服务
===================

当NASA POWER API不可用时，提供模拟天气数据用于测试和演示
"""

import math
import random
from datetime import datetime, timedelta
from typing import List
import numpy as np

# 使用本地WeatherData定义，避免循环导入
from dataclasses import dataclass
from src.core.logger import get_logger

logger = get_logger(__name__)

@dataclass
class WeatherData:
    """天气数据结构"""
    timestamp: datetime
    ghi: float  # 全球水平辐照度 (W/m²)
    dhi: float  # 散射水平辐照度 (W/m²)
    dni: float  # 直射法向辐照度 (W/m²)
    clearsky_ghi: float  # 晴空全球水平辐照度 (W/m²)
    temperature: float  # 温度 (°C)
    temperature_max: float  # 最高温度 (°C)
    temperature_min: float  # 最低温度 (°C)
    humidity: float  # 相对湿度 (%)
    wind_speed: float  # 风速 (m/s)
    precipitation: float  # 降水量 (mm)
    cloud_cover: float  # 云量 (%)

class MockWeatherService:
    """模拟天气数据服务"""
    
    def __init__(self):
        """初始化模拟天气服务"""
        self.base_ghi = 800  # 基础全球水平辐照度 (W/m²)
        self.base_temp = 25  # 基础温度 (°C)
        random.seed(42)  # 确保可重现的结果
        logger.info("模拟天气服务初始化完成")
    
    def get_historical_data(self, latitude: float, longitude: float,
                          start_date: datetime, end_date: datetime) -> List[WeatherData]:
        """
        生成模拟历史天气数据
        
        Args:
            latitude: 纬度
            longitude: 经度
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            模拟天气数据列表
        """
        logger.info(f"生成模拟历史天气数据: {start_date} 到 {end_date}")
        
        weather_data_list = []
        current_time = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        
        while current_time <= end_date:
            weather_data = self._generate_weather_point(current_time, latitude, longitude)
            weather_data_list.append(weather_data)
            current_time += timedelta(hours=1)
        
        logger.info(f"生成了 {len(weather_data_list)} 条模拟天气数据")
        return weather_data_list
    
    def get_forecast_data(self, latitude: float, longitude: float,
                         forecast_hours: int = 24) -> List[WeatherData]:
        """
        生成模拟预测天气数据
        
        Args:
            latitude: 纬度
            longitude: 经度
            forecast_hours: 预测小时数
            
        Returns:
            模拟预测天气数据列表
        """
        logger.info(f"生成 {forecast_hours} 小时的模拟预测天气数据")
        
        weather_data_list = []
        current_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        
        for i in range(forecast_hours):
            forecast_time = current_time + timedelta(hours=i+1)
            weather_data = self._generate_weather_point(forecast_time, latitude, longitude, is_forecast=True)
            weather_data_list.append(weather_data)
        
        logger.info(f"生成了 {len(weather_data_list)} 条模拟预测数据")
        return weather_data_list
    
    def _generate_weather_point(self, timestamp: datetime, latitude: float, 
                               longitude: float, is_forecast: bool = False) -> WeatherData:
        """
        生成单个时间点的模拟天气数据
        
        Args:
            timestamp: 时间戳
            latitude: 纬度
            longitude: 经度
            is_forecast: 是否为预测数据
            
        Returns:
            天气数据点
        """
        hour = timestamp.hour
        day_of_year = timestamp.timetuple().tm_yday
        
        # 计算太阳高度角影响
        solar_elevation = self._calculate_solar_elevation(hour, day_of_year, latitude)
        
        # 基础辐照度（考虑太阳高度角）
        if solar_elevation > 0:
            ghi_base = self.base_ghi * math.sin(math.radians(solar_elevation))
        else:
            ghi_base = 0
        
        # 添加随机变化和天气模式
        weather_factor = 0.7 + 0.6 * random.random()  # 0.7-1.3的天气系数
        cloud_factor = 0.5 + 0.5 * math.sin(day_of_year * 2 * math.pi / 365)  # 季节性云量变化
        
        ghi = max(0, ghi_base * weather_factor * cloud_factor)
        
        # 计算其他辐照度分量
        dhi = ghi * (0.1 + 0.1 * random.random())  # 散射辐照度
        dni = max(0, ghi - dhi)  # 直射辐照度
        clearsky_ghi = ghi_base  # 晴空辐照度
        
        # 温度计算（考虑纬度、季节、时间）
        seasonal_temp = 10 * math.sin((day_of_year - 80) * 2 * math.pi / 365)  # 季节变化
        daily_temp = 8 * math.sin((hour - 6) * math.pi / 12)  # 日变化
        latitude_factor = (90 - abs(latitude)) / 90  # 纬度影响
        
        temperature = self.base_temp + seasonal_temp + daily_temp * latitude_factor
        temperature += random.gauss(0, 2)  # 添加随机噪声
        
        # 其他气象参数
        humidity = max(30, min(90, 60 + 20 * math.sin(hour * math.pi / 12) + random.gauss(0, 10)))
        wind_speed = max(0, 5 + 3 * random.random() + 2 * math.sin(hour * math.pi / 24))
        precipitation = 0 if random.random() > 0.1 else np.random.exponential(2)
        cloud_cover = max(0, min(100, 50 * (1 - weather_factor) + random.gauss(0, 20)))
        
        # 如果是预测数据，添加一些不确定性
        if is_forecast:
            uncertainty = 1 + random.gauss(0, 0.1)  # ±10%的不确定性
            ghi *= uncertainty
            temperature += random.gauss(0, 1)
        
        return WeatherData(
            timestamp=timestamp,
            ghi=round(max(0, ghi), 1),
            dhi=round(max(0, dhi), 1),
            dni=round(max(0, dni), 1),
            clearsky_ghi=round(max(0, clearsky_ghi), 1),
            temperature=round(temperature, 1),
            temperature_max=round(temperature + 3, 1),
            temperature_min=round(temperature - 3, 1),
            humidity=round(max(0, min(100, humidity)), 1),
            wind_speed=round(max(0, wind_speed), 1),
            precipitation=round(max(0, precipitation), 1),
            cloud_cover=round(max(0, min(100, cloud_cover)), 1)
        )
    
    def _calculate_solar_elevation(self, hour: int, day_of_year: int, latitude: float) -> float:
        """
        计算太阳高度角
        
        Args:
            hour: 小时 (0-23)
            day_of_year: 一年中的第几天
            latitude: 纬度
            
        Returns:
            太阳高度角 (度)
        """
        # 简化的太阳高度角计算
        declination = 23.45 * math.sin(math.radians(360 * (284 + day_of_year) / 365))
        hour_angle = 15 * (hour - 12)
        
        elevation = math.asin(
            math.sin(math.radians(declination)) * math.sin(math.radians(latitude)) +
            math.cos(math.radians(declination)) * math.cos(math.radians(latitude)) * 
            math.cos(math.radians(hour_angle))
        )
        
        return math.degrees(elevation)
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return True  # 模拟服务总是可用
    
    def get_service_info(self) -> dict:
        """获取服务信息"""
        return {
            "name": "Mock Weather Service",
            "description": "模拟天气数据服务，用于测试和演示",
            "version": "1.0.0",
            "data_source": "模拟生成",
            "accuracy": "仅用于测试，不代表真实天气"
        }
