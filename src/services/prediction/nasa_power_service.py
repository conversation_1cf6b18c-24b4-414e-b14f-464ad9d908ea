"""
NASA POWER API 服务
==================

基于NASA POWER API获取光照和天气数据的服务。
"""

import asyncio
import aiohttp
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
from dataclasses import dataclass
import json
import time

from src.core.config import config
from src.core.logger import get_logger
from src.core.exceptions import VPPAIException

logger = get_logger(__name__)


@dataclass
class WeatherData:
    """天气数据结构"""
    timestamp: datetime
    ghi: float  # 全球水平辐照度 (W/m²)
    dhi: float  # 散射水平辐照度 (W/m²)
    dni: float  # 直射法向辐照度 (W/m²)
    clearsky_ghi: float  # 晴空全球水平辐照度 (W/m²)
    temperature: float  # 温度 (°C)
    temperature_max: float  # 最高温度 (°C)
    temperature_min: float  # 最低温度 (°C)
    humidity: float  # 相对湿度 (%)
    wind_speed: float  # 风速 (m/s)
    precipitation: float  # 降水量 (mm)
    cloud_cover: float  # 云量 (%)


class NASAPowerService:
    """NASA POWER API 服务类"""

    def __init__(self):
        """初始化NASA POWER服务"""
        self.base_url = config.get('nasa_power.base_url')
        self.community = config.get('nasa_power.community', 'RE')
        self.time_standard = config.get('nasa_power.time_standard', 'LST')
        self.format = config.get('nasa_power.format', 'JSON')
        self.parameters = config.get('nasa_power.parameters', [])

        # 缓存配置
        self.cache_enabled = config.get('nasa_power.cache_enabled', True)
        self.cache_ttl = config.get('nasa_power.cache_ttl', 3600)
        self._cache = {}

        # 请求限制
        self.max_retries = config.get('nasa_power.max_retries', 3)
        self.timeout = config.get('nasa_power.timeout', 30)
        self.rate_limit = config.get('nasa_power.rate_limit', 60)
        self._last_request_time = 0

        # 备用服务
        self.use_mock_fallback = True
        self._mock_service = None

        logger.info("NASA POWER服务初始化完成")
    
    def _check_rate_limit(self) -> None:
        """检查请求频率限制"""
        current_time = time.time()
        time_since_last = current_time - self._last_request_time
        min_interval = 60 / self.rate_limit  # 最小请求间隔
        
        if time_since_last < min_interval:
            sleep_time = min_interval - time_since_last
            logger.info(f"请求频率限制，等待 {sleep_time:.2f} 秒")
            time.sleep(sleep_time)
        
        self._last_request_time = time.time()

    def _get_mock_service(self):
        """获取模拟服务实例"""
        if self._mock_service is None:
            from src.services.prediction.mock_weather_service import MockWeatherService
            self._mock_service = MockWeatherService()
        return self._mock_service

    def _get_mock_data(self, latitude: float, longitude: float,
                      start_date: datetime, end_date: datetime) -> List[WeatherData]:
        """获取模拟数据"""
        if self.use_mock_fallback:
            mock_service = self._get_mock_service()
            return mock_service.get_historical_data(latitude, longitude, start_date, end_date)
        return []

    def _get_cache_key(self, latitude: float, longitude: float,
                      start_date: str, end_date: str) -> str:
        """生成缓存键"""
        return f"nasa_power_{latitude}_{longitude}_{start_date}_{end_date}"
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """检查缓存是否有效"""
        if not self.cache_enabled:
            return False
        
        cache_time = cache_entry.get('timestamp', 0)
        return (time.time() - cache_time) < self.cache_ttl
    
    def get_historical_data(self, latitude: float, longitude: float,
                          start_date: datetime, end_date: datetime) -> List[WeatherData]:
        """
        获取历史天气数据

        Args:
            latitude: 纬度
            longitude: 经度
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            天气数据列表
        """
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')

        # 检查缓存
        cache_key = self._get_cache_key(latitude, longitude, start_str, end_str)
        if cache_key in self._cache and self._is_cache_valid(self._cache[cache_key]):
            logger.info(f"从缓存获取数据: {cache_key}")
            return self._cache[cache_key]['data']

        try:
            # 构建请求参数
            params = {
                'parameters': ','.join(self.parameters),
                'community': self.community,
                'longitude': longitude,
                'latitude': latitude,
                'start': start_str,
                'end': end_str,
                'format': self.format,
                'time-standard': self.time_standard
            }

            # 发送请求
            weather_data = self._make_request(params)

            # 解析数据
            parsed_data = self._parse_response(weather_data, latitude, longitude)

            # 缓存数据
            if self.cache_enabled:
                self._cache[cache_key] = {
                    'data': parsed_data,
                    'timestamp': time.time()
                }

            return parsed_data

        except Exception as e:
            logger.warning(f"NASA POWER API失败，使用备用模拟数据: {e}")
            return self._get_mock_data(latitude, longitude, start_date, end_date)
    
    def _make_request(self, params: Dict) -> Dict:
        """发送HTTP请求"""
        self._check_rate_limit()
        
        for attempt in range(self.max_retries):
            try:
                logger.info(f"发送NASA POWER API请求 (尝试 {attempt + 1}/{self.max_retries})")
                
                response = requests.get(
                    self.base_url,
                    params=params,
                    timeout=self.timeout
                )
                
                response.raise_for_status()
                data = response.json()
                
                if 'properties' not in data:
                    raise VPPAIException(f"NASA POWER API响应格式错误: {data}")
                
                logger.info("NASA POWER API请求成功")
                return data
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"NASA POWER API请求失败 (尝试 {attempt + 1}): {e}")
                if attempt == self.max_retries - 1:
                    raise VPPAIException(f"NASA POWER API请求失败: {e}")
                time.sleep(2 ** attempt)  # 指数退避
        
        raise VPPAIException("NASA POWER API请求超过最大重试次数")
    
    def _parse_response(self, response_data: Dict, latitude: float, 
                       longitude: float) -> List[WeatherData]:
        """解析API响应数据"""
        try:
            properties = response_data['properties']
            parameter_data = properties['parameter']

            # 调试信息
            logger.info(f"API返回的参数: {list(parameter_data.keys())}")

            # 获取时间序列数据
            weather_data_list = []

            # 假设所有参数都有相同的时间戳
            if not parameter_data:
                logger.warning("API返回的参数数据为空")
                return []

            first_param = list(parameter_data.keys())[0]
            timestamps = list(parameter_data[first_param].keys())
            logger.info(f"找到 {len(timestamps)} 个时间戳，示例: {timestamps[:3] if timestamps else '无'}")
            
            for timestamp_str in timestamps:
                # 解析时间戳 - 支持多种格式
                timestamp = None
                try:
                    if len(timestamp_str) == 10:  # YYYYMMDDHH
                        timestamp = datetime.strptime(timestamp_str, '%Y%m%d%H')
                    elif len(timestamp_str) == 8:  # YYYYMMDD
                        timestamp = datetime.strptime(timestamp_str, '%Y%m%d')
                    else:
                        logger.warning(f"未知的时间戳格式: {timestamp_str}")
                        continue
                except ValueError as e:
                    logger.warning(f"时间戳解析失败: {timestamp_str}, 错误: {e}")
                    continue

                if timestamp is None:
                    continue
                
                # 提取各参数值
                ghi = parameter_data.get('ALLSKY_SFC_SW_DWN', {}).get(timestamp_str, 0)
                temperature = parameter_data.get('T2M', {}).get(timestamp_str, 0)
                humidity = parameter_data.get('RH2M', {}).get(timestamp_str, 0)
                wind_speed = parameter_data.get('WS10M', {}).get(timestamp_str, 0)

                # 调试信息 - 只打印前几个
                if len(weather_data_list) < 3:
                    logger.info(f"时间戳 {timestamp_str}: GHI={ghi}, T2M={temperature}, RH2M={humidity}, WS10M={wind_speed}")

                # 处理缺失值
                if any(val == -999 for val in [ghi, temperature]):
                    if len(weather_data_list) < 3:
                        logger.info(f"跳过缺失值数据: {timestamp_str}")
                    continue

                # 设置默认值或计算值
                dhi = ghi * 0.1  # 散射辐照度约为GHI的10%
                dni = ghi * 0.8  # 直射辐照度约为GHI的80%
                clearsky_ghi = ghi * 1.2  # 晴空辐照度
                temp_max = temperature + 5  # 估算最高温度
                temp_min = temperature - 5  # 估算最低温度
                precipitation = 0  # 默认无降水
                cloud_cover = max(0, min(100, (1 - ghi/1000) * 100))  # 根据辐照度估算云量
                
                weather_data = WeatherData(
                    timestamp=timestamp,
                    ghi=max(0, ghi),  # 确保非负值
                    dhi=max(0, dhi),
                    dni=max(0, dni),
                    clearsky_ghi=max(0, clearsky_ghi),
                    temperature=temperature,
                    temperature_max=temp_max,
                    temperature_min=temp_min,
                    humidity=max(0, min(100, humidity)),  # 限制在0-100%
                    wind_speed=max(0, wind_speed),
                    precipitation=max(0, precipitation),
                    cloud_cover=max(0, min(100, cloud_cover))
                )
                
                weather_data_list.append(weather_data)
            
            logger.info(f"解析得到 {len(weather_data_list)} 条天气数据")

            # 如果没有有效数据，使用模拟数据
            if len(weather_data_list) == 0:
                logger.warning("NASA POWER API返回的数据全部为缺失值，使用模拟数据")
                return []

            return weather_data_list
            
        except Exception as e:
            logger.error(f"解析NASA POWER API响应失败: {e}")
            raise VPPAIException(f"解析NASA POWER API响应失败: {e}")
    
    def get_forecast_data(self, latitude: float, longitude: float,
                         forecast_hours: int = 24) -> List[WeatherData]:
        """
        获取预测天气数据

        注意：NASA POWER API主要提供历史数据，实时预测数据可能有5-7天延迟
        这里返回最近的历史数据作为近似预测

        Args:
            latitude: 纬度
            longitude: 经度
            forecast_hours: 预测小时数

        Returns:
            预测天气数据列表
        """
        logger.warning("NASA POWER API主要提供历史数据，预测功能有限")

        try:
            # 获取最近几天的数据作为预测基础
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)

            historical_data = self.get_historical_data(latitude, longitude, start_date, end_date)

            # 简单的预测：使用最近24小时的数据模式
            if len(historical_data) >= 24:
                # 取最后24小时的数据作为预测模板
                recent_pattern = historical_data[-24:]

                forecast_data = []
                base_time = datetime.now().replace(minute=0, second=0, microsecond=0)

                for i in range(forecast_hours):
                    pattern_index = i % 24
                    template = recent_pattern[pattern_index]

                    # 创建预测数据点（添加一些随机变化）
                    forecast_point = WeatherData(
                        timestamp=base_time + timedelta(hours=i+1),
                        ghi=template.ghi * (0.9 + 0.2 * (i % 3) / 3),  # 简单变化
                        dhi=template.dhi * (0.9 + 0.2 * (i % 3) / 3),
                        dni=template.dni * (0.9 + 0.2 * (i % 3) / 3),
                        clearsky_ghi=template.clearsky_ghi,
                        temperature=template.temperature,
                        temperature_max=template.temperature_max,
                        temperature_min=template.temperature_min,
                        humidity=template.humidity,
                        wind_speed=template.wind_speed,
                        precipitation=template.precipitation,
                        cloud_cover=template.cloud_cover
                    )

                    forecast_data.append(forecast_point)

                return forecast_data

        except Exception as e:
            logger.warning(f"预测数据获取失败，使用备用模拟数据: {e}")

        # 使用模拟服务生成预测数据
        if self.use_mock_fallback:
            mock_service = self._get_mock_service()
            return mock_service.get_forecast_data(latitude, longitude, forecast_hours)

        return []
    
    def to_dataframe(self, weather_data: List[WeatherData]) -> pd.DataFrame:
        """将天气数据转换为DataFrame"""
        data_dict = {
            'timestamp': [wd.timestamp for wd in weather_data],
            'ghi': [wd.ghi for wd in weather_data],
            'dhi': [wd.dhi for wd in weather_data],
            'dni': [wd.dni for wd in weather_data],
            'clearsky_ghi': [wd.clearsky_ghi for wd in weather_data],
            'temperature': [wd.temperature for wd in weather_data],
            'temperature_max': [wd.temperature_max for wd in weather_data],
            'temperature_min': [wd.temperature_min for wd in weather_data],
            'humidity': [wd.humidity for wd in weather_data],
            'wind_speed': [wd.wind_speed for wd in weather_data],
            'precipitation': [wd.precipitation for wd in weather_data],
            'cloud_cover': [wd.cloud_cover for wd in weather_data]
        }
        
        df = pd.DataFrame(data_dict)
        df.set_index('timestamp', inplace=True)
        return df
