"""
天气数据处理工具
================

提供天气数据处理、转换和分析的工具函数。
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import math

from src.core.logger import get_logger

logger = get_logger(__name__)


def calculate_solar_position(latitude: float, longitude: float, 
                           timestamp: datetime) -> Tuple[float, float]:
    """
    计算太阳位置（太阳高度角和方位角）
    
    Args:
        latitude: 纬度（度）
        longitude: 经度（度）
        timestamp: 时间戳
        
    Returns:
        Tuple[太阳高度角, 太阳方位角]（度）
    """
    # 转换为弧度
    lat_rad = math.radians(latitude)
    
    # 计算一年中的天数
    day_of_year = timestamp.timetuple().tm_yday
    
    # 计算太阳赤纬角
    declination = math.radians(23.45) * math.sin(math.radians(360 * (284 + day_of_year) / 365))
    
    # 计算时角
    hour = timestamp.hour + timestamp.minute / 60.0

    # 处理时区偏移
    if timestamp.utcoffset() is not None:
        utc_offset_hours = timestamp.utcoffset().total_seconds() / 3600
    else:
        utc_offset_hours = 0  # 假设为UTC时间

    solar_time = hour + (longitude - 15 * utc_offset_hours) / 15
    hour_angle = math.radians(15 * (solar_time - 12))
    
    # 计算太阳高度角
    elevation = math.asin(
        math.sin(declination) * math.sin(lat_rad) +
        math.cos(declination) * math.cos(lat_rad) * math.cos(hour_angle)
    )
    
    # 计算太阳方位角
    azimuth = math.atan2(
        math.sin(hour_angle),
        math.cos(hour_angle) * math.sin(lat_rad) - math.tan(declination) * math.cos(lat_rad)
    )
    
    # 转换为度
    elevation_deg = math.degrees(elevation)
    azimuth_deg = math.degrees(azimuth)
    
    # 调整方位角到0-360度范围
    if azimuth_deg < 0:
        azimuth_deg += 360
    
    return elevation_deg, azimuth_deg


def calculate_clear_sky_irradiance(latitude: float, longitude: float,
                                 timestamp: datetime, altitude: float = 0) -> float:
    """
    计算晴空辐照度
    
    Args:
        latitude: 纬度（度）
        longitude: 经度（度）
        timestamp: 时间戳
        altitude: 海拔高度（米）
        
    Returns:
        晴空辐照度（W/m²）
    """
    # 太阳常数
    solar_constant = 1367  # W/m²
    
    # 计算太阳位置
    elevation, _ = calculate_solar_position(latitude, longitude, timestamp)
    
    # 如果太阳在地平线以下，返回0
    if elevation <= 0:
        return 0
    
    # 计算大气质量
    air_mass = 1 / (math.sin(math.radians(elevation)) + 0.50572 * (elevation + 6.07995) ** -1.6364)
    
    # 考虑海拔高度的大气压力修正
    pressure_ratio = math.exp(-altitude / 8400)  # 标准大气模型
    air_mass *= pressure_ratio
    
    # 计算大气透射率（简化模型）
    transmittance = 0.7 ** (air_mass ** 0.678)
    
    # 计算晴空辐照度
    clear_sky_irradiance = solar_constant * transmittance * math.sin(math.radians(elevation))
    
    return max(0, clear_sky_irradiance)


def calculate_diffuse_fraction(ghi: float, clearsky_ghi: float) -> float:
    """
    计算散射辐射比例
    
    Args:
        ghi: 全球水平辐照度
        clearsky_ghi: 晴空全球水平辐照度
        
    Returns:
        散射辐射比例（0-1）
    """
    if clearsky_ghi <= 0:
        return 0.5  # 默认值
    
    # 晴空指数
    clearness_index = ghi / clearsky_ghi
    clearness_index = max(0, min(1, clearness_index))
    
    # Erbs模型计算散射比例
    if clearness_index <= 0.22:
        diffuse_fraction = 1.0 - 0.09 * clearness_index
    elif clearness_index <= 0.8:
        diffuse_fraction = (0.9511 - 0.1604 * clearness_index + 
                          4.388 * clearness_index**2 - 
                          16.638 * clearness_index**3 + 
                          12.336 * clearness_index**4)
    else:
        diffuse_fraction = 0.165
    
    return max(0, min(1, diffuse_fraction))


def calculate_tilted_irradiance(ghi: float, dhi: float, dni: float,
                               panel_tilt: float, panel_azimuth: float,
                               sun_elevation: float, sun_azimuth: float,
                               albedo: float = 0.2) -> float:
    """
    计算倾斜面辐照度
    
    Args:
        ghi: 全球水平辐照度
        dhi: 散射水平辐照度
        dni: 直射法向辐照度
        panel_tilt: 光伏板倾斜角（度）
        panel_azimuth: 光伏板方位角（度）
        sun_elevation: 太阳高度角（度）
        sun_azimuth: 太阳方位角（度）
        albedo: 地面反射率
        
    Returns:
        倾斜面辐照度（W/m²）
    """
    if sun_elevation <= 0:
        return 0
    
    # 转换为弧度
    panel_tilt_rad = math.radians(panel_tilt)
    panel_azimuth_rad = math.radians(panel_azimuth)
    sun_elevation_rad = math.radians(sun_elevation)
    sun_azimuth_rad = math.radians(sun_azimuth)
    
    # 计算入射角余弦值
    cos_incidence = (math.sin(sun_elevation_rad) * math.cos(panel_tilt_rad) +
                    math.cos(sun_elevation_rad) * math.sin(panel_tilt_rad) *
                    math.cos(sun_azimuth_rad - panel_azimuth_rad))
    
    cos_incidence = max(0, cos_incidence)
    
    # 直射辐射分量
    beam_component = dni * cos_incidence
    
    # 散射辐射分量（各向同性天空模型）
    diffuse_component = dhi * (1 + math.cos(panel_tilt_rad)) / 2
    
    # 地面反射分量
    reflected_component = ghi * albedo * (1 - math.cos(panel_tilt_rad)) / 2
    
    # 总倾斜面辐照度
    tilted_irradiance = beam_component + diffuse_component + reflected_component
    
    return max(0, tilted_irradiance)


def interpolate_weather_data(weather_data: List[Dict], target_timestamps: List[datetime]) -> List[Dict]:
    """
    插值天气数据到目标时间戳
    
    Args:
        weather_data: 原始天气数据列表
        target_timestamps: 目标时间戳列表
        
    Returns:
        插值后的天气数据列表
    """
    if not weather_data or not target_timestamps:
        return []
    
    # 转换为DataFrame进行插值
    df = pd.DataFrame(weather_data)
    if 'timestamp' not in df.columns:
        logger.warning("天气数据中缺少timestamp列")
        return []
    
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    df.set_index('timestamp', inplace=True)
    
    # 创建目标时间索引
    target_index = pd.DatetimeIndex(target_timestamps)
    
    # 重新索引并插值
    df_reindexed = df.reindex(df.index.union(target_index))
    df_interpolated = df_reindexed.interpolate(method='linear')
    
    # 提取目标时间点的数据
    result_df = df_interpolated.loc[target_index]
    
    # 转换回字典列表
    result = []
    for timestamp, row in result_df.iterrows():
        data_point = {'timestamp': timestamp}
        data_point.update(row.to_dict())
        result.append(data_point)
    
    return result


def smooth_weather_data(weather_data: List[Dict], window_size: int = 3) -> List[Dict]:
    """
    平滑天气数据
    
    Args:
        weather_data: 天气数据列表
        window_size: 平滑窗口大小
        
    Returns:
        平滑后的天气数据列表
    """
    if len(weather_data) < window_size:
        return weather_data
    
    df = pd.DataFrame(weather_data)
    
    # 对数值列进行平滑
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    
    for col in numeric_columns:
        if col != 'timestamp':
            df[col] = df[col].rolling(window=window_size, center=True, min_periods=1).mean()
    
    return df.to_dict('records')


def detect_weather_anomalies(weather_data: List[Dict], threshold: float = 3.0) -> List[int]:
    """
    检测天气数据异常值
    
    Args:
        weather_data: 天气数据列表
        threshold: 异常值检测阈值（标准差倍数）
        
    Returns:
        异常值索引列表
    """
    if len(weather_data) < 10:
        return []
    
    df = pd.DataFrame(weather_data)
    anomaly_indices = []
    
    # 检查主要天气参数
    check_columns = ['ghi', 'temperature', 'humidity', 'wind_speed']
    
    for col in check_columns:
        if col in df.columns:
            mean = df[col].mean()
            std = df[col].std()
            
            # 找出超过阈值的数据点
            anomalies = np.abs(df[col] - mean) > threshold * std
            anomaly_indices.extend(df[anomalies].index.tolist())
    
    # 去重并排序
    anomaly_indices = sorted(list(set(anomaly_indices)))
    
    logger.info(f"检测到 {len(anomaly_indices)} 个天气数据异常值")
    return anomaly_indices


def calculate_weather_quality_score(weather_data: Dict) -> float:
    """
    计算天气质量评分
    
    Args:
        weather_data: 单个天气数据点
        
    Returns:
        天气质量评分（0-100）
    """
    score = 100.0
    
    # 辐照度评分（权重40%）
    ghi = weather_data.get('ghi', 0)
    if ghi > 800:
        irradiance_score = 100
    elif ghi > 600:
        irradiance_score = 80 + (ghi - 600) / 200 * 20
    elif ghi > 400:
        irradiance_score = 60 + (ghi - 400) / 200 * 20
    elif ghi > 200:
        irradiance_score = 40 + (ghi - 200) / 200 * 20
    else:
        irradiance_score = ghi / 200 * 40
    
    # 云量评分（权重30%）
    cloud_cover = weather_data.get('cloud_cover', 50)
    cloud_score = max(0, 100 - cloud_cover)
    
    # 温度评分（权重20%）
    temperature = weather_data.get('temperature', 25)
    if 20 <= temperature <= 30:
        temp_score = 100
    elif 15 <= temperature <= 35:
        temp_score = 80
    elif 10 <= temperature <= 40:
        temp_score = 60
    else:
        temp_score = 40
    
    # 风速评分（权重10%）
    wind_speed = weather_data.get('wind_speed', 0)
    if wind_speed < 2:
        wind_score = 100
    elif wind_speed < 5:
        wind_score = 90
    elif wind_speed < 10:
        wind_score = 70
    else:
        wind_score = 50
    
    # 加权平均
    total_score = (irradiance_score * 0.4 + 
                  cloud_score * 0.3 + 
                  temp_score * 0.2 + 
                  wind_score * 0.1)
    
    return round(total_score, 1)


def format_weather_summary(weather_data: List[Dict]) -> Dict:
    """
    生成天气数据摘要
    
    Args:
        weather_data: 天气数据列表
        
    Returns:
        天气摘要字典
    """
    if not weather_data:
        return {}
    
    df = pd.DataFrame(weather_data)
    
    summary = {
        'period': {
            'start': df['timestamp'].min() if 'timestamp' in df.columns else None,
            'end': df['timestamp'].max() if 'timestamp' in df.columns else None,
            'data_points': len(df)
        },
        'irradiance': {
            'average_ghi': df['ghi'].mean() if 'ghi' in df.columns else 0,
            'peak_ghi': df['ghi'].max() if 'ghi' in df.columns else 0,
            'total_irradiation': df['ghi'].sum() / 1000 if 'ghi' in df.columns else 0  # kWh/m²
        },
        'temperature': {
            'average': df['temperature'].mean() if 'temperature' in df.columns else 0,
            'min': df['temperature'].min() if 'temperature' in df.columns else 0,
            'max': df['temperature'].max() if 'temperature' in df.columns else 0
        },
        'conditions': {
            'average_cloud_cover': df['cloud_cover'].mean() if 'cloud_cover' in df.columns else 0,
            'average_humidity': df['humidity'].mean() if 'humidity' in df.columns else 0,
            'average_wind_speed': df['wind_speed'].mean() if 'wind_speed' in df.columns else 0,
            'total_precipitation': df['precipitation'].sum() if 'precipitation' in df.columns else 0
        }
    }
    
    # 计算平均天气质量评分
    quality_scores = [calculate_weather_quality_score(data) for data in weather_data]
    summary['quality'] = {
        'average_score': np.mean(quality_scores),
        'min_score': np.min(quality_scores),
        'max_score': np.max(quality_scores)
    }
    
    return summary
