#!/usr/bin/env python3
"""
🌞 VPP-AI 光伏发电预测系统启动脚本
===================================

提供多种启动方式：
1. Streamlit UI (推荐)
2. Flask Web UI
3. 同时启动两个服务
"""

import os
import sys
import subprocess
import argparse
import time
import signal
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'streamlit',
        'flask',
        'plotly',
        'folium',
        'pandas',
        'numpy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def start_streamlit():
    """启动Streamlit UI"""
    print("🚀 启动Streamlit光伏预测UI...")
    print("📍 访问地址: http://localhost:8501")
    
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        "solar_prediction_ui.py",
        "--server.port=8501",
        "--server.address=0.0.0.0",
        "--browser.gatherUsageStats=false"
    ]
    
    return subprocess.Popen(cmd)

def start_flask():
    """启动Flask后端服务"""
    print("🚀 启动Flask后端服务...")
    print("📍 访问地址: http://localhost:5000")
    
    cmd = [sys.executable, "solar_ui_backend.py"]
    return subprocess.Popen(cmd)

def start_both():
    """同时启动两个服务"""
    print("🚀 启动完整的光伏预测系统...")
    print("📍 Streamlit UI: http://localhost:8501")
    print("📍 Flask API: http://localhost:5000")
    
    flask_process = start_flask()
    time.sleep(2)  # 等待Flask启动
    streamlit_process = start_streamlit()
    
    return [flask_process, streamlit_process]

def signal_handler(sig, frame, processes):
    """信号处理器，用于优雅关闭"""
    print("\n🛑 正在关闭服务...")
    for process in processes:
        if process and process.poll() is None:
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
    print("✅ 服务已关闭")
    sys.exit(0)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="VPP-AI 光伏发电预测系统启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_solar_ui.py                    # 启动Streamlit UI (默认)
  python run_solar_ui.py --mode streamlit   # 启动Streamlit UI
  python run_solar_ui.py --mode flask       # 启动Flask后端
  python run_solar_ui.py --mode both        # 同时启动两个服务
  python run_solar_ui.py --check            # 检查依赖
        """
    )
    
    parser.add_argument(
        '--mode', 
        choices=['streamlit', 'flask', 'both'],
        default='streamlit',
        help='启动模式 (默认: streamlit)'
    )
    
    parser.add_argument(
        '--check',
        action='store_true',
        help='检查依赖包是否安装'
    )
    
    parser.add_argument(
        '--no-browser',
        action='store_true',
        help='不自动打开浏览器'
    )
    
    args = parser.parse_args()
    
    # 检查依赖
    if args.check or not check_dependencies():
        return
    
    # 确保在正确的目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("🌞 VPP-AI 光伏发电预测系统")
    print("=" * 50)
    
    processes = []
    
    try:
        if args.mode == 'streamlit':
            processes = [start_streamlit()]
        elif args.mode == 'flask':
            processes = [start_flask()]
        elif args.mode == 'both':
            processes = start_both()
        
        # 设置信号处理器
        signal.signal(signal.SIGINT, lambda s, f: signal_handler(s, f, processes))
        signal.signal(signal.SIGTERM, lambda s, f: signal_handler(s, f, processes))
        
        print("\n✅ 服务启动成功！")
        print("💡 按 Ctrl+C 停止服务")
        
        # 等待进程结束
        for process in processes:
            if process:
                process.wait()
                
    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None, processes)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        for process in processes:
            if process and process.poll() is None:
                process.terminate()
        sys.exit(1)

if __name__ == "__main__":
    main()
