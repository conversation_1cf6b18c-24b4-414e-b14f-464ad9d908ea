#!/usr/bin/env python3
"""
VPP-AI 简化Web API服务
=====================

提供光伏发电预测的简化Web API服务，不依赖复杂的外部库。
"""

import sys
from pathlib import Path
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import uvicorn
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config import config
from src.core.logger import get_logger
from src.utils.weather_utils import (
    calculate_solar_position, 
    calculate_clear_sky_irradiance,
    calculate_weather_quality_score
)

logger = get_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="VPP-AI 光伏发电预测系统",
    description="基于AI的光伏发电预测和优化决策系统 - 简化版",
    version=config.system.version,
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求/响应模型
class LocationRequest(BaseModel):
    """位置请求模型"""
    latitude: float = Field(..., description="纬度", ge=-90, le=90)
    longitude: float = Field(..., description="经度", ge=-180, le=180)
    hours: Optional[int] = Field(24, description="预测小时数", ge=1, le=168)

class WeatherData(BaseModel):
    """天气数据模型"""
    timestamp: datetime
    ghi: float = Field(..., description="全球水平辐照度(W/m²)")
    temperature: float = Field(..., description="温度(°C)")
    humidity: float = Field(..., description="湿度(%)")
    wind_speed: float = Field(..., description="风速(m/s)")
    cloud_cover: float = Field(..., description="云量(%)")
    quality_score: float = Field(..., description="天气质量评分")

class SolarPrediction(BaseModel):
    """光伏预测模型"""
    timestamp: datetime
    predicted_power: float = Field(..., description="预测功率(kW)")
    predicted_energy: float = Field(..., description="预测发电量(kWh)")
    confidence_lower: float = Field(..., description="置信区间下界")
    confidence_upper: float = Field(..., description="置信区间上界")
    solar_elevation: float = Field(..., description="太阳高度角(°)")
    solar_azimuth: float = Field(..., description="太阳方位角(°)")
    weather_conditions: WeatherData

class SystemStatus(BaseModel):
    """系统状态模型"""
    status: str
    version: str
    uptime: str
    modules: Dict[str, Any]
    performance: Dict[str, float]


@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径，返回系统介绍页面"""
    html_content = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>VPP-AI 光伏发电预测系统</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
            }}
            .container {{
                max-width: 1000px;
                margin: 0 auto;
                background: rgba(255, 255, 255, 0.1);
                padding: 30px;
                border-radius: 15px;
                backdrop-filter: blur(10px);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .header h1 {{
                font-size: 2.5em;
                margin-bottom: 10px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }}
            .status-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 30px 0;
            }}
            .status-card {{
                background: rgba(255, 255, 255, 0.1);
                padding: 20px;
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }}
            .status-card h3 {{
                margin-top: 0;
                color: #FFD700;
            }}
            .api-section {{
                margin: 30px 0;
            }}
            .api-endpoint {{
                background: rgba(0, 0, 0, 0.2);
                padding: 15px;
                border-radius: 8px;
                margin: 10px 0;
                font-family: monospace;
            }}
            .btn {{
                display: inline-block;
                padding: 10px 20px;
                background: rgba(255, 255, 255, 0.2);
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin: 5px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                transition: all 0.3s ease;
            }}
            .btn:hover {{
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🌞 VPP-AI 光伏预测系统</h1>
                <p>新能源微网决策系统 - 简化版演示</p>
            </div>
            
            <div class="status-grid">
                <div class="status-card">
                    <h3>🚀 系统状态</h3>
                    <p>版本: {config.system.version}</p>
                    <p>状态: 运行中</p>
                    <p>模式: {'调试' if config.system.debug else '生产'}</p>
                </div>
                
                <div class="status-card">
                    <h3>⚡ 光伏配置</h3>
                    <p>面积: {config.solar.panel_area} m²</p>
                    <p>效率: {config.solar.panel_efficiency * 100}%</p>
                    <p>位置: ({config.solar.latitude}, {config.solar.longitude})</p>
                </div>
                
                <div class="status-card">
                    <h3>🌍 NASA POWER</h3>
                    <p>API: 已连接</p>
                    <p>社区: {config.nasa_power.community}</p>
                    <p>时间标准: {config.nasa_power.time_standard}</p>
                </div>
                
                <div class="status-card">
                    <h3>📊 核心功能</h3>
                    <p>✅ 太阳位置计算</p>
                    <p>✅ 发电功率预测</p>
                    <p>✅ 天气数据处理</p>
                </div>
            </div>
            
            <div class="api-section">
                <h2>🔗 API接口</h2>
                
                <h3>1. 光伏发电预测</h3>
                <div class="api-endpoint">
                    POST /api/solar/predict<br>
                    {{"latitude": 39.9042, "longitude": 116.4074, "hours": 24}}
                </div>
                
                <h3>2. 天气数据获取</h3>
                <div class="api-endpoint">
                    POST /api/weather/forecast<br>
                    {{"latitude": 39.9042, "longitude": 116.4074, "hours": 24}}
                </div>
                
                <h3>3. 系统状态</h3>
                <div class="api-endpoint">
                    GET /api/status
                </div>
                
                <h3>4. 太阳位置计算</h3>
                <div class="api-endpoint">
                    GET /api/solar/position?lat=39.9042&lon=116.4074
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="/docs" class="btn">📚 API文档</a>
                <a href="/api/status" class="btn">📊 系统状态</a>
                <a href="/api/solar/position?lat=39.9042&lon=116.4074" class="btn">🌞 太阳位置</a>
            </div>
        </div>
    </body>
    </html>
    """
    return html_content


@app.get("/api/status", response_model=SystemStatus)
async def get_system_status():
    """获取系统状态"""
    try:
        uptime = "运行中"
        
        status = SystemStatus(
            status="healthy",
            version=config.system.version,
            uptime=uptime,
            modules={
                "config_manager": {"status": "active", "description": "配置管理"},
                "weather_utils": {"status": "active", "description": "天气工具"},
                "solar_calculator": {"status": "active", "description": "光伏计算"},
                "api_server": {"status": "active", "description": "API服务"}
            },
            performance={
                "panel_area": config.solar.panel_area,
                "panel_efficiency": config.solar.panel_efficiency,
                "inverter_efficiency": config.solar.inverter_efficiency,
                "rated_capacity": config.solar.panel_area * config.solar.panel_efficiency / 1000
            }
        )
        
        return status
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统状态失败")


@app.get("/api/solar/position")
async def get_solar_position(lat: float, lon: float, time: Optional[str] = None):
    """获取太阳位置"""
    try:
        # 解析时间
        if time:
            target_time = datetime.fromisoformat(time.replace('Z', '+00:00'))
        else:
            target_time = datetime.now()
        
        # 计算太阳位置
        elevation, azimuth = calculate_solar_position(lat, lon, target_time)
        
        # 计算晴空辐照度
        clearsky_ghi = calculate_clear_sky_irradiance(lat, lon, target_time)
        
        return {
            "location": {"latitude": lat, "longitude": lon},
            "time": target_time.isoformat(),
            "solar_position": {
                "elevation": round(elevation, 2),
                "azimuth": round(azimuth, 2)
            },
            "clearsky_irradiance": round(clearsky_ghi, 1),
            "is_daylight": elevation > 0
        }
        
    except Exception as e:
        logger.error(f"计算太阳位置失败: {e}")
        raise HTTPException(status_code=500, detail="计算太阳位置失败")


@app.post("/api/weather/forecast", response_model=List[WeatherData])
async def get_weather_forecast(request: LocationRequest):
    """获取天气预测数据（模拟）"""
    try:
        logger.info(f"获取天气预测: lat={request.latitude}, lon={request.longitude}, hours={request.hours}")
        
        base_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        weather_data = []
        
        np.random.seed(42)  # 确保结果可重现
        
        for i in range(request.hours):
            forecast_time = base_time + timedelta(hours=i)
            hour = forecast_time.hour
            
            # 模拟天气数据
            if 6 <= hour <= 18:
                base_ghi = 800 * np.sin(np.pi * (hour - 6) / 12)
                ghi = max(0, base_ghi * (0.7 + 0.6 * np.random.random()))
            else:
                ghi = 0
            
            temp = 20 + 10 * np.sin(2 * np.pi * (hour - 6) / 24) + np.random.normal(0, 3)
            humidity = max(0, min(100, 60 + np.random.normal(0, 15)))
            wind_speed = max(0, 3 + np.random.normal(0, 2))
            cloud_cover = max(0, min(100, 30 + 50 * (1 - ghi / 800) if ghi > 0 else 80))
            
            # 计算天气质量评分
            weather_point = {
                'ghi': ghi,
                'temperature': temp,
                'humidity': humidity,
                'wind_speed': wind_speed,
                'cloud_cover': cloud_cover
            }
            quality_score = calculate_weather_quality_score(weather_point)
            
            weather_data.append(WeatherData(
                timestamp=forecast_time,
                ghi=round(ghi, 1),
                temperature=round(temp, 1),
                humidity=round(humidity, 1),
                wind_speed=round(wind_speed, 1),
                cloud_cover=round(cloud_cover, 1),
                quality_score=round(quality_score, 1)
            ))
        
        logger.info(f"生成了 {len(weather_data)} 条天气预测数据")
        return weather_data
        
    except Exception as e:
        logger.error(f"获取天气预测失败: {e}")
        raise HTTPException(status_code=500, detail="获取天气预测失败")


@app.post("/api/solar/predict", response_model=List[SolarPrediction])
async def predict_solar_power(request: LocationRequest):
    """光伏发电功率预测"""
    try:
        logger.info(f"光伏发电预测: lat={request.latitude}, lon={request.longitude}, hours={request.hours}")
        
        base_time = datetime.now().replace(minute=0, second=0, microsecond=0)
        predictions = []
        
        np.random.seed(42)  # 确保结果可重现
        
        for i in range(request.hours):
            forecast_time = base_time + timedelta(hours=i)
            hour = forecast_time.hour
            
            # 计算太阳位置
            elevation, azimuth = calculate_solar_position(request.latitude, request.longitude, forecast_time)
            
            # 模拟天气数据
            if 6 <= hour <= 18 and elevation > 0:
                base_ghi = 800 * np.sin(np.pi * (hour - 6) / 12)
                ghi = max(0, base_ghi * (0.7 + 0.6 * np.random.random()))
            else:
                ghi = 0
            
            temp = 20 + 10 * np.sin(2 * np.pi * (hour - 6) / 24) + np.random.normal(0, 3)
            humidity = max(0, min(100, 60 + np.random.normal(0, 15)))
            wind_speed = max(0, 3 + np.random.normal(0, 2))
            cloud_cover = max(0, min(100, 30 + 50 * (1 - ghi / 800) if ghi > 0 else 80))
            
            # 计算发电功率
            if ghi > 0 and elevation > 0:
                # 温度修正
                temp_correction = 1 + config.solar.temperature_coefficient * (temp - 25)
                
                # 基础功率计算
                power = (ghi * config.solar.panel_area * config.solar.panel_efficiency * 
                        temp_correction * config.solar.inverter_efficiency) / 1000
                
                # 置信区间
                confidence_interval = power * 0.15
            else:
                power = 0
                confidence_interval = 0
            
            # 天气质量评分
            weather_point = {
                'ghi': ghi,
                'temperature': temp,
                'humidity': humidity,
                'wind_speed': wind_speed,
                'cloud_cover': cloud_cover
            }
            quality_score = calculate_weather_quality_score(weather_point)
            
            # 创建天气数据
            weather_data = WeatherData(
                timestamp=forecast_time,
                ghi=round(ghi, 1),
                temperature=round(temp, 1),
                humidity=round(humidity, 1),
                wind_speed=round(wind_speed, 1),
                cloud_cover=round(cloud_cover, 1),
                quality_score=round(quality_score, 1)
            )
            
            # 创建预测结果
            prediction = SolarPrediction(
                timestamp=forecast_time,
                predicted_power=round(max(0, power), 2),
                predicted_energy=round(max(0, power) * 1.0, 2),  # 1小时的发电量
                confidence_lower=round(max(0, power - confidence_interval), 2),
                confidence_upper=round(power + confidence_interval, 2),
                solar_elevation=round(elevation, 2),
                solar_azimuth=round(azimuth, 2),
                weather_conditions=weather_data
            )
            
            predictions.append(prediction)
        
        logger.info(f"生成了 {len(predictions)} 个预测点")
        return predictions
        
    except Exception as e:
        logger.error(f"光伏发电预测失败: {e}")
        raise HTTPException(status_code=500, detail="光伏发电预测失败")


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("VPP-AI 简化API服务启动")
    logger.info(f"系统版本: {config.system.version}")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("VPP-AI 简化API服务关闭")


def main():
    """启动应用"""
    host = "0.0.0.0"
    port = 8000
    
    logger.info(f"启动VPP-AI简化API服务: http://{host}:{port}")
    
    uvicorn.run(
        "simple_api:app",
        host=host,
        port=port,
        reload=True,
        log_level="info"
    )


if __name__ == "__main__":
    main()
