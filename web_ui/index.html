<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌞 VPP-AI 光伏发电预测系统</title>
    
    <!-- CSS 样式 -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    
    <!-- 谷歌地图 API -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap&libraries=places">
    </script>
    
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-solar-panel"></i>
                <span>VPP-AI 光伏预测系统</span>
            </div>
            <div class="nav-status">
                <span class="status-indicator" id="apiStatus">
                    <i class="fas fa-circle"></i> API连接中...
                </span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-section">
                <h3><i class="fas fa-map-marker-alt"></i> 项目位置</h3>
                <div class="location-info">
                    <div class="input-group">
                        <label>项目名称</label>
                        <input type="text" id="projectName" placeholder="输入项目名称" value="示例光伏项目">
                    </div>
                    <div class="input-group">
                        <label>地址搜索</label>
                        <input type="text" id="addressSearch" placeholder="搜索地址或点击地图选择">
                    </div>
                    <div class="coordinate-display">
                        <div class="coord-item">
                            <label>纬度</label>
                            <span id="latitude">39.9042</span>
                        </div>
                        <div class="coord-item">
                            <label>经度</label>
                            <span id="longitude">116.4074</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="sidebar-section">
                <h3><i class="fas fa-cog"></i> 光伏配置</h3>
                <div class="solar-config">
                    <div class="config-header">
                        <button class="btn btn-primary" id="addSolarGroup">
                            <i class="fas fa-plus"></i> 添加光伏组
                        </button>
                    </div>
                    <div id="solarGroups" class="solar-groups">
                        <!-- 光伏组配置将动态添加到这里 -->
                    </div>
                </div>
            </div>

            <div class="sidebar-section">
                <h3><i class="fas fa-calendar-alt"></i> 分析设置</h3>
                <div class="analysis-config">
                    <div class="input-group">
                        <label>历史分析开始日期</label>
                        <input type="date" id="historyStartDate">
                    </div>
                    <div class="input-group">
                        <label>历史分析结束日期</label>
                        <input type="date" id="historyEndDate">
                    </div>
                    <div class="input-group">
                        <label>预测时长</label>
                        <select id="forecastHours">
                            <option value="24">24小时</option>
                            <option value="48">48小时</option>
                            <option value="72">72小时</option>
                            <option value="168">7天</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label>数据精度</label>
                        <select id="dataPrecision">
                            <option value="hourly">小时级</option>
                            <option value="daily">日级</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="sidebar-section">
                <h3><i class="fas fa-play"></i> 操作控制</h3>
                <div class="control-buttons">
                    <button class="btn btn-success" id="runHistoryAnalysis">
                        <i class="fas fa-history"></i> 历史分析
                    </button>
                    <button class="btn btn-primary" id="runForecast">
                        <i class="fas fa-chart-line"></i> 发电预测
                    </button>
                    <button class="btn btn-secondary" id="exportData">
                        <i class="fas fa-download"></i> 导出数据
                    </button>
                </div>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 地图区域 -->
            <section class="map-section">
                <div class="section-header">
                    <h2><i class="fas fa-map"></i> 项目位置选择</h2>
                    <div class="map-controls">
                        <button class="btn btn-sm" id="locateMe">
                            <i class="fas fa-crosshairs"></i> 定位我的位置
                        </button>
                    </div>
                </div>
                <div id="map" class="map-container"></div>
            </section>

            <!-- 系统概览 -->
            <section class="overview-section">
                <div class="section-header">
                    <h2><i class="fas fa-tachometer-alt"></i> 系统概览</h2>
                </div>
                <div class="overview-cards">
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-solar-panel"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="totalCapacity">0 kW</h3>
                            <p>总装机容量</p>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="totalGroups">0</h3>
                            <p>光伏组数量</p>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-chart-area"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="totalArea">0 m²</h3>
                            <p>总面积</p>
                        </div>
                    </div>
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="avgEfficiency">0%</h3>
                            <p>平均效率</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 图表区域 -->
            <section class="charts-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-line"></i> 数据分析</h2>
                    <div class="chart-controls">
                        <div class="chart-tabs">
                            <button class="tab-btn active" data-chart="power">发电功率</button>
                            <button class="tab-btn" data-chart="irradiance">辐照度</button>
                            <button class="tab-btn" data-chart="weather">天气条件</button>
                            <button class="tab-btn" data-chart="comparison">对比分析</button>
                        </div>
                    </div>
                </div>
                
                <div class="charts-container">
                    <!-- 发电功率图表 -->
                    <div class="chart-panel active" id="powerChart">
                        <canvas id="powerCanvas"></canvas>
                    </div>
                    
                    <!-- 辐照度图表 -->
                    <div class="chart-panel" id="irradianceChart">
                        <canvas id="irradianceCanvas"></canvas>
                    </div>
                    
                    <!-- 天气条件图表 -->
                    <div class="chart-panel" id="weatherChart">
                        <canvas id="weatherCanvas"></canvas>
                    </div>
                    
                    <!-- 对比分析图表 -->
                    <div class="chart-panel" id="comparisonChart">
                        <canvas id="comparisonCanvas"></canvas>
                    </div>
                </div>
            </section>

            <!-- 数据表格 -->
            <section class="data-section">
                <div class="section-header">
                    <h2><i class="fas fa-table"></i> 详细数据</h2>
                    <div class="data-controls">
                        <button class="btn btn-sm" id="toggleTable">
                            <i class="fas fa-eye"></i> 显示/隐藏表格
                        </button>
                    </div>
                </div>
                <div class="data-table-container" id="dataTableContainer">
                    <table class="data-table" id="dataTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>预测功率 (kW)</th>
                                <th>置信区间</th>
                                <th>GHI (W/m²)</th>
                                <th>温度 (°C)</th>
                                <th>天气质量</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <!-- 数据行将动态添加 -->
                        </tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>正在处理数据...</p>
        </div>
    </div>

    <!-- 通知消息 -->
    <div class="notification" id="notification">
        <div class="notification-content">
            <i class="fas fa-info-circle"></i>
            <span class="notification-message"></span>
        </div>
    </div>

    <!-- JavaScript 文件 -->
    <script src="app.js"></script>
</body>
</html>
