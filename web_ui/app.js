/**
 * VPP-AI 光伏发电预测系统 JavaScript
 * ===================================
 * 
 * 处理地图交互、图表显示、数据可视化等前端功能
 */

// 全局变量
let map;
let marker;
let charts = {};
let currentLocation = { lat: 39.9042, lng: 116.4074 };
let solarGroups = [];
let predictionData = null;
let historicalData = null;

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * 初始化应用
 */
function initializeApp() {
    console.log('🌞 VPP-AI 光伏预测系统启动中...');
    
    // 检查API状态
    checkAPIStatus();
    
    // 初始化事件监听器
    initializeEventListeners();
    
    // 初始化图表
    initializeCharts();
    
    // 设置默认日期
    setDefaultDates();
    
    console.log('✅ 应用初始化完成');
}

/**
 * 检查API状态
 */
async function checkAPIStatus() {
    const statusElement = document.getElementById('apiStatus');
    
    try {
        const response = await fetch('/api/status');
        if (response.ok) {
            statusElement.innerHTML = '<i class="fas fa-circle"></i> API连接正常';
            statusElement.className = 'status-indicator status-success';
        } else {
            throw new Error('API响应异常');
        }
    } catch (error) {
        statusElement.innerHTML = '<i class="fas fa-circle"></i> API连接失败';
        statusElement.className = 'status-indicator status-error';
        console.error('API状态检查失败:', error);
    }
}

/**
 * 初始化谷歌地图
 */
function initMap() {
    console.log('🗺️ 初始化谷歌地图...');
    
    // 创建地图
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 10,
        center: currentLocation,
        mapTypeId: 'satellite',
        styles: [
            {
                featureType: 'all',
                elementType: 'labels',
                stylers: [{ visibility: 'on' }]
            }
        ]
    });
    
    // 创建标记
    marker = new google.maps.Marker({
        position: currentLocation,
        map: map,
        title: '项目位置',
        draggable: true,
        icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="#FF6B35">
                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
                </svg>
            `),
            scaledSize: new google.maps.Size(40, 40),
            anchor: new google.maps.Point(20, 20)
        }
    });
    
    // 监听标记拖拽
    marker.addListener('dragend', function(event) {
        currentLocation = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
        };
        updateLocationDisplay();
    });
    
    // 监听地图点击
    map.addListener('click', function(event) {
        currentLocation = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
        };
        marker.setPosition(currentLocation);
        updateLocationDisplay();
    });
    
    // 初始化地址搜索
    initializeAddressSearch();
    
    console.log('✅ 谷歌地图初始化完成');
}

/**
 * 初始化地址搜索
 */
function initializeAddressSearch() {
    const searchInput = document.getElementById('addressSearch');
    const autocomplete = new google.maps.places.Autocomplete(searchInput);
    
    autocomplete.addListener('place_changed', function() {
        const place = autocomplete.getPlace();
        if (place.geometry) {
            currentLocation = {
                lat: place.geometry.location.lat(),
                lng: place.geometry.location.lng()
            };
            map.setCenter(currentLocation);
            marker.setPosition(currentLocation);
            updateLocationDisplay();
        }
    });
}

/**
 * 更新位置显示
 */
function updateLocationDisplay() {
    document.getElementById('latitude').textContent = currentLocation.lat.toFixed(4);
    document.getElementById('longitude').textContent = currentLocation.lng.toFixed(4);
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 添加光伏组
    document.getElementById('addSolarGroup').addEventListener('click', showAddSolarGroupModal);
    
    // 定位按钮
    document.getElementById('locateMe').addEventListener('click', locateUser);
    
    // 分析按钮
    document.getElementById('runHistoryAnalysis').addEventListener('click', runHistoryAnalysis);
    document.getElementById('runForecast').addEventListener('click', runForecast);
    document.getElementById('exportData').addEventListener('click', exportData);
    
    // 图表切换
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            switchChart(this.dataset.chart);
        });
    });
    
    // 表格切换
    document.getElementById('toggleTable').addEventListener('click', toggleDataTable);
}

/**
 * 初始化图表
 */
function initializeCharts() {
    const chartConfigs = {
        power: {
            type: 'line',
            data: { labels: [], datasets: [] },
            options: {
                responsive: true,
                plugins: {
                    title: { display: true, text: '发电功率预测' },
                    legend: { position: 'top' }
                },
                scales: {
                    y: { beginAtZero: true, title: { display: true, text: '功率 (kW)' } },
                    x: { title: { display: true, text: '时间' } }
                }
            }
        },
        irradiance: {
            type: 'line',
            data: { labels: [], datasets: [] },
            options: {
                responsive: true,
                plugins: {
                    title: { display: true, text: '太阳辐照度' },
                    legend: { position: 'top' }
                },
                scales: {
                    y: { beginAtZero: true, title: { display: true, text: '辐照度 (W/m²)' } },
                    x: { title: { display: true, text: '时间' } }
                }
            }
        },
        weather: {
            type: 'line',
            data: { labels: [], datasets: [] },
            options: {
                responsive: true,
                plugins: {
                    title: { display: true, text: '天气条件' },
                    legend: { position: 'top' }
                },
                scales: {
                    y: { title: { display: true, text: '温度 (°C)' } },
                    x: { title: { display: true, text: '时间' } }
                }
            }
        },
        comparison: {
            type: 'bar',
            data: { labels: [], datasets: [] },
            options: {
                responsive: true,
                plugins: {
                    title: { display: true, text: '对比分析' },
                    legend: { position: 'top' }
                },
                scales: {
                    y: { beginAtZero: true, title: { display: true, text: '发电量 (kWh)' } }
                }
            }
        }
    };
    
    // 创建图表实例
    Object.keys(chartConfigs).forEach(chartType => {
        const canvas = document.getElementById(`${chartType}Canvas`);
        if (canvas) {
            charts[chartType] = new Chart(canvas, chartConfigs[chartType]);
        }
    });
}

/**
 * 切换图表显示
 */
function switchChart(chartType) {
    // 更新标签页状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-chart="${chartType}"]`).classList.add('active');
    
    // 切换图表面板
    document.querySelectorAll('.chart-panel').forEach(panel => {
        panel.classList.remove('active');
    });
    document.getElementById(`${chartType}Chart`).classList.add('active');
}

/**
 * 设置默认日期
 */
function setDefaultDates() {
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    document.getElementById('historyEndDate').value = today.toISOString().split('T')[0];
    document.getElementById('historyStartDate').value = thirtyDaysAgo.toISOString().split('T')[0];
}

/**
 * 用户定位
 */
function locateUser() {
    if (navigator.geolocation) {
        showLoading('正在获取您的位置...');
        
        navigator.geolocation.getCurrentPosition(
            function(position) {
                currentLocation = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                
                if (map) {
                    map.setCenter(currentLocation);
                    marker.setPosition(currentLocation);
                }
                
                updateLocationDisplay();
                hideLoading();
                showNotification('定位成功！', 'success');
            },
            function(error) {
                hideLoading();
                showNotification('定位失败：' + error.message, 'error');
            }
        );
    } else {
        showNotification('您的浏览器不支持地理定位', 'error');
    }
}

/**
 * 显示加载指示器
 */
function showLoading(message = '正在处理数据...') {
    const overlay = document.getElementById('loadingOverlay');
    const messageElement = overlay.querySelector('p');
    messageElement.textContent = message;
    overlay.classList.add('show');
}

/**
 * 隐藏加载指示器
 */
function hideLoading() {
    document.getElementById('loadingOverlay').classList.remove('show');
}

/**
 * 显示通知消息
 */
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    const messageElement = notification.querySelector('.notification-message');
    const iconElement = notification.querySelector('i');
    
    messageElement.textContent = message;
    
    // 设置图标和样式
    switch (type) {
        case 'success':
            iconElement.className = 'fas fa-check-circle';
            notification.style.borderLeft = '4px solid #28a745';
            break;
        case 'error':
            iconElement.className = 'fas fa-exclamation-circle';
            notification.style.borderLeft = '4px solid #dc3545';
            break;
        case 'warning':
            iconElement.className = 'fas fa-exclamation-triangle';
            notification.style.borderLeft = '4px solid #ffc107';
            break;
        default:
            iconElement.className = 'fas fa-info-circle';
            notification.style.borderLeft = '4px solid #17a2b8';
    }
    
    // 显示通知
    notification.classList.add('show');
    
    // 3秒后自动隐藏
    setTimeout(() => {
        notification.classList.remove('show');
    }, 3000);
}

/**
 * 切换数据表格显示
 */
function toggleDataTable() {
    const container = document.getElementById('dataTableContainer');
    const button = document.getElementById('toggleTable');
    
    if (container.style.display === 'none') {
        container.style.display = 'block';
        button.innerHTML = '<i class="fas fa-eye-slash"></i> 隐藏表格';
    } else {
        container.style.display = 'none';
        button.innerHTML = '<i class="fas fa-eye"></i> 显示表格';
    }
}

// 错误处理
window.addEventListener('error', function(event) {
    console.error('JavaScript错误:', event.error);
    showNotification('系统发生错误，请刷新页面重试', 'error');
});

/**
 * 运行历史分析
 */
async function runHistoryAnalysis() {
    if (solarGroups.length === 0) {
        showNotification('请先配置光伏系统', 'warning');
        return;
    }

    const startDate = document.getElementById('historyStartDate').value;
    const endDate = document.getElementById('historyEndDate').value;
    const precision = document.getElementById('dataPrecision').value;

    if (!startDate || !endDate) {
        showNotification('请选择分析日期范围', 'warning');
        return;
    }

    showLoading('正在进行历史分析...');

    try {
        const response = await fetch('/api/historical-analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                location: currentLocation,
                solar_groups: solarGroups,
                start_date: startDate,
                end_date: endDate,
                precision: precision
            })
        });

        const result = await response.json();

        if (result.success) {
            historicalData = result.data;
            updateHistoricalCharts();
            updateDataTable(historicalData, 'historical');
            showNotification(result.message, 'success');
        } else {
            showNotification(result.message || '历史分析失败', 'error');
        }

    } catch (error) {
        console.error('历史分析错误:', error);
        showNotification('网络错误，请检查连接', 'error');
    } finally {
        hideLoading();
    }
}

/**
 * 运行发电预测
 */
async function runForecast() {
    if (solarGroups.length === 0) {
        showNotification('请先配置光伏系统', 'warning');
        return;
    }

    const forecastHours = parseInt(document.getElementById('forecastHours').value);

    showLoading('正在进行AI预测...');

    try {
        const response = await fetch('/api/power-prediction', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                location: currentLocation,
                solar_groups: solarGroups,
                forecast_hours: forecastHours,
                model_type: 'ensemble'
            })
        });

        const result = await response.json();

        if (result.success) {
            predictionData = result.data;
            updatePredictionCharts();
            updateDataTable(predictionData, 'prediction');
            showNotification(result.message, 'success');
        } else {
            showNotification(result.message || '预测失败', 'error');
        }

    } catch (error) {
        console.error('预测错误:', error);
        showNotification('网络错误，请检查连接', 'error');
    } finally {
        hideLoading();
    }
}

/**
 * 更新历史数据图表
 */
function updateHistoricalCharts() {
    if (!historicalData || historicalData.length === 0) return;

    // 按光伏组分组数据
    const groupedData = {};
    historicalData.forEach(item => {
        if (!groupedData[item.group_name]) {
            groupedData[item.group_name] = [];
        }
        groupedData[item.group_name].push(item);
    });

    // 更新发电功率图表
    updatePowerChart(groupedData, 'historical');

    // 更新辐照度图表
    updateIrradianceChart(groupedData);

    // 更新天气图表
    updateWeatherChart(groupedData);

    // 更新对比图表
    updateComparisonChart(groupedData, 'historical');
}

/**
 * 更新预测数据图表
 */
function updatePredictionCharts() {
    if (!predictionData || predictionData.length === 0) return;

    // 按光伏组分组数据
    const groupedData = {};
    predictionData.forEach(item => {
        if (!groupedData[item.group_name]) {
            groupedData[item.group_name] = [];
        }
        groupedData[item.group_name].push(item);
    });

    // 更新发电功率图表（包含置信区间）
    updatePowerChart(groupedData, 'prediction');

    // 更新辐照度图表
    updateIrradianceChart(groupedData);

    // 更新天气图表
    updateWeatherChart(groupedData);

    // 更新对比图表
    updateComparisonChart(groupedData, 'prediction');
}

/**
 * 更新发电功率图表
 */
function updatePowerChart(groupedData, type) {
    const chart = charts.power;
    const colors = ['#FF6B35', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

    chart.data.labels = [];
    chart.data.datasets = [];

    let colorIndex = 0;

    Object.keys(groupedData).forEach(groupName => {
        const data = groupedData[groupName];
        const color = colors[colorIndex % colors.length];

        // 时间标签
        if (chart.data.labels.length === 0) {
            chart.data.labels = data.map(item =>
                new Date(item.timestamp).toLocaleString('zh-CN', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit'
                })
            );
        }

        // 主要数据线
        const powerData = type === 'prediction' ?
            data.map(item => item.predicted_power) :
            data.map(item => item.power);

        chart.data.datasets.push({
            label: `${groupName} - 功率`,
            data: powerData,
            borderColor: color,
            backgroundColor: color + '20',
            borderWidth: 2,
            fill: false,
            tension: 0.1
        });

        // 如果是预测数据，添加置信区间
        if (type === 'prediction') {
            chart.data.datasets.push({
                label: `${groupName} - 上界`,
                data: data.map(item => item.upper_bound),
                borderColor: color + '60',
                backgroundColor: color + '10',
                borderWidth: 1,
                fill: '+1',
                tension: 0.1,
                pointRadius: 0
            });

            chart.data.datasets.push({
                label: `${groupName} - 下界`,
                data: data.map(item => item.lower_bound),
                borderColor: color + '60',
                backgroundColor: color + '10',
                borderWidth: 1,
                fill: false,
                tension: 0.1,
                pointRadius: 0
            });
        }

        colorIndex++;
    });

    chart.update();
}

/**
 * 更新辐照度图表
 */
function updateIrradianceChart(groupedData) {
    const chart = charts.irradiance;
    const firstGroup = Object.values(groupedData)[0];

    if (!firstGroup) return;

    chart.data.labels = firstGroup.map(item =>
        new Date(item.timestamp).toLocaleString('zh-CN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit'
        })
    );

    chart.data.datasets = [{
        label: 'GHI 辐照度',
        data: firstGroup.map(item => item.ghi),
        borderColor: '#FFA726',
        backgroundColor: '#FFA72620',
        borderWidth: 2,
        fill: true,
        tension: 0.1
    }];

    chart.update();
}

/**
 * 更新天气图表
 */
function updateWeatherChart(groupedData) {
    const chart = charts.weather;
    const firstGroup = Object.values(groupedData)[0];

    if (!firstGroup) return;

    chart.data.labels = firstGroup.map(item =>
        new Date(item.timestamp).toLocaleString('zh-CN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit'
        })
    );

    chart.data.datasets = [{
        label: '环境温度',
        data: firstGroup.map(item => item.temperature),
        borderColor: '#EF5350',
        backgroundColor: '#EF535020',
        borderWidth: 2,
        fill: true,
        tension: 0.1
    }];

    chart.update();
}

/**
 * 更新对比图表
 */
function updateComparisonChart(groupedData, type) {
    const chart = charts.comparison;

    chart.data.labels = Object.keys(groupedData);

    // 计算每组的总发电量
    const totalEnergy = Object.keys(groupedData).map(groupName => {
        const data = groupedData[groupName];
        const powerData = type === 'prediction' ?
            data.map(item => item.predicted_power) :
            data.map(item => item.power);
        return powerData.reduce((sum, power) => sum + power, 0);
    });

    chart.data.datasets = [{
        label: type === 'prediction' ? '预测发电量 (kWh)' : '历史发电量 (kWh)',
        data: totalEnergy,
        backgroundColor: [
            '#FF6B35',
            '#4ECDC4',
            '#45B7D1',
            '#96CEB4',
            '#FFEAA7'
        ].slice(0, totalEnergy.length),
        borderWidth: 1
    }];

    chart.update();
}

/**
 * 更新数据表格
 */
function updateDataTable(data, type) {
    const tableBody = document.getElementById('dataTableBody');
    tableBody.innerHTML = '';

    // 只显示前50条数据
    const displayData = data.slice(0, 50);

    displayData.forEach(item => {
        const row = document.createElement('tr');

        const timestamp = new Date(item.timestamp).toLocaleString('zh-CN');
        const power = type === 'prediction' ? item.predicted_power : item.power;
        const confidence = type === 'prediction' ?
            `${item.lower_bound.toFixed(1)} - ${item.upper_bound.toFixed(1)}` :
            '-';

        row.innerHTML = `
            <td>${timestamp}</td>
            <td>${power.toFixed(2)}</td>
            <td>${confidence}</td>
            <td>${item.ghi.toFixed(1)}</td>
            <td>${item.temperature.toFixed(1)}</td>
            <td>${getWeatherQuality(item.ghi)}</td>
        `;

        tableBody.appendChild(row);
    });
}

/**
 * 获取天气质量描述
 */
function getWeatherQuality(ghi) {
    if (ghi > 800) return '优秀';
    if (ghi > 600) return '良好';
    if (ghi > 400) return '一般';
    if (ghi > 200) return '较差';
    return '很差';
}

/**
 * 导出数据
 */
function exportData() {
    const data = predictionData || historicalData;

    if (!data || data.length === 0) {
        showNotification('没有可导出的数据', 'warning');
        return;
    }

    // 转换为CSV格式
    const headers = ['时间', '光伏组', '功率(kW)', 'GHI(W/m²)', '温度(°C)'];
    const csvContent = [
        headers.join(','),
        ...data.map(item => [
            new Date(item.timestamp).toLocaleString('zh-CN'),
            item.group_name,
            (item.predicted_power || item.power).toFixed(2),
            item.ghi.toFixed(1),
            item.temperature.toFixed(1)
        ].join(','))
    ].join('\n');

    // 下载文件
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `solar_data_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('数据导出成功', 'success');
}

// 确保谷歌地图API加载完成后初始化地图
window.initMap = initMap;
