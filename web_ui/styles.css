/* VPP-AI 光伏发电预测系统 CSS 样式 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* 导航栏样式 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #FF6B35;
}

.nav-brand i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.nav-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background: #e8f5e8;
    color: #2d5a2d;
    font-size: 0.9rem;
}

.status-indicator i {
    margin-right: 0.5rem;
    color: #28a745;
}

/* 主容器样式 */
.main-container {
    max-width: 1400px;
    margin: 2rem auto;
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 2rem;
    padding: 0 2rem;
}

/* 侧边栏样式 */
.sidebar {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 1.5rem;
    height: fit-content;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section h3 {
    color: #333;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    font-size: 1.1rem;
}

.sidebar-section h3 i {
    margin-right: 0.5rem;
    color: #FF6B35;
}

/* 输入组样式 */
.input-group {
    margin-bottom: 1rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #FF6B35;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

/* 坐标显示样式 */
.coordinate-display {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
}

.coord-item {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 8px;
    text-align: center;
}

.coord-item label {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.coord-item span {
    font-weight: bold;
    color: #333;
}

/* 光伏配置样式 */
.solar-config {
    margin-top: 1rem;
}

.config-header {
    margin-bottom: 1rem;
}

.solar-groups {
    max-height: 400px;
    overflow-y: auto;
}

.solar-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: transform 0.2s ease;
}

.solar-group:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.group-name {
    font-weight: bold;
    color: #333;
}

.group-params {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #666;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

.btn i {
    margin-right: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 107, 53, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.control-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* 主内容区域样式 */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* 区域样式 */
.map-section,
.overview-section,
.charts-section,
.data-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f3f4;
}

.section-header h2 {
    color: #333;
    display: flex;
    align-items: center;
    font-size: 1.3rem;
}

.section-header h2 i {
    margin-right: 0.5rem;
    color: #FF6B35;
}

/* 地图样式 */
.map-container {
    height: 400px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.map-controls {
    display: flex;
    gap: 0.5rem;
}

/* 概览卡片样式 */
.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.overview-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-5px);
}

.card-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
    opacity: 0.8;
}

.card-content h3 {
    font-size: 1.8rem;
    margin-bottom: 0.25rem;
}

.card-content p {
    opacity: 0.9;
    font-size: 0.9rem;
}

/* 图表样式 */
.chart-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.chart-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.25rem;
}

.tab-btn {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.tab-btn.active {
    background: #FF6B35;
    color: white;
}

.tab-btn:hover:not(.active) {
    background: rgba(255, 107, 53, 0.1);
}

.charts-container {
    position: relative;
    min-height: 400px;
}

.chart-panel {
    display: none;
}

.chart-panel.active {
    display: block;
}

/* 数据表格样式 */
.data-table-container {
    max-height: 500px;
    overflow: auto;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
}

.data-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

/* 加载和通知样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.loading-spinner i {
    font-size: 2rem;
    color: #FF6B35;
    margin-bottom: 1rem;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
}

.notification-content i {
    margin-right: 0.5rem;
    color: #FF6B35;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .overview-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }
    
    .main-container {
        padding: 0 1rem;
    }
    
    .overview-cards {
        grid-template-columns: 1fr;
    }
    
    .chart-tabs {
        flex-wrap: wrap;
    }
    
    .coordinate-display {
        grid-template-columns: 1fr;
    }
}
