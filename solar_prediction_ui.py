#!/usr/bin/env python3
"""
🌞 VPP-AI 光伏发电预测 UI 界面
=====================================

基于Streamlit的交互式光伏发电预测界面，集成谷歌地图、多组光伏配置、
NASA POWER API数据获取和AI预测模型。
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import folium
from streamlit_folium import st_folium
from datetime import datetime, timedelta
import json
import requests
from typing import Dict, List, Tuple, Optional
import asyncio
import time

# 导入项目模块
from src.services.prediction.solar_prediction_service import SolarPredictionService
from src.services.prediction.nasa_power_service import NASAPowerService
from src.models.solar import SolarPanel
from src.core.config import config
from src.core.logger import get_logger

# 配置页面
st.set_page_config(
    page_title="🌞 VPP-AI 光伏发电预测系统",
    page_icon="🌞",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化日志
logger = get_logger(__name__)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #FF6B35 0%, #F7931E 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #FF6B35;
    }
    
    .solar-group-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin-bottom: 1rem;
    }
    
    .status-success {
        color: #28a745;
        font-weight: bold;
    }
    
    .status-warning {
        color: #ffc107;
        font-weight: bold;
    }
    
    .status-error {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

class SolarPredictionUI:
    """光伏发电预测UI主类"""
    
    def __init__(self):
        """初始化UI组件"""
        self.prediction_service = None
        self.nasa_service = None
        self.solar_groups = []
        self.current_location = {"lat": 39.9042, "lng": 116.4074}  # 默认北京
        
        # 初始化服务
        self._init_services()
        
        # 初始化会话状态
        self._init_session_state()
    
    def _init_services(self):
        """初始化预测服务"""
        try:
            self.nasa_service = NASAPowerService()
            self.prediction_service = SolarPredictionService()
            st.session_state.services_status = "✅ 服务连接正常"
        except Exception as e:
            st.session_state.services_status = f"❌ 服务连接失败: {str(e)}"
            logger.error(f"服务初始化失败: {e}")
    
    def _init_session_state(self):
        """初始化会话状态"""
        if 'solar_groups' not in st.session_state:
            st.session_state.solar_groups = []
        
        if 'current_location' not in st.session_state:
            st.session_state.current_location = self.current_location
        
        if 'prediction_results' not in st.session_state:
            st.session_state.prediction_results = None
        
        if 'historical_data' not in st.session_state:
            st.session_state.historical_data = None
    
    def render_header(self):
        """渲染页面头部"""
        st.markdown("""
        <div class="main-header">
            <h1>🌞 VPP-AI 光伏发电预测系统</h1>
            <p>基于NASA POWER数据和AI模型的高精度光伏发电预测平台</p>
        </div>
        """, unsafe_allow_html=True)
        
        # 状态指示器
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            st.markdown(f"**服务状态:** {st.session_state.get('services_status', '初始化中...')}")
        with col2:
            st.markdown(f"**当前位置:** {st.session_state.current_location['lat']:.4f}, {st.session_state.current_location['lng']:.4f}")
        with col3:
            st.markdown(f"**光伏组数:** {len(st.session_state.solar_groups)}")
    
    def render_location_selector(self):
        """渲染位置选择器"""
        st.subheader("📍 项目位置选择")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            # 创建地图
            m = folium.Map(
                location=[st.session_state.current_location['lat'], st.session_state.current_location['lng']],
                zoom_start=10,
                tiles='OpenStreetMap'
            )
            
            # 添加标记
            folium.Marker(
                [st.session_state.current_location['lat'], st.session_state.current_location['lng']],
                popup="项目位置",
                tooltip="点击地图选择新位置",
                icon=folium.Icon(color='red', icon='solar-panel', prefix='fa')
            ).add_to(m)
            
            # 显示地图
            map_data = st_folium(m, width=700, height=400)
            
            # 更新位置
            if map_data['last_clicked']:
                st.session_state.current_location = {
                    'lat': map_data['last_clicked']['lat'],
                    'lng': map_data['last_clicked']['lng']
                }
                st.rerun()
        
        with col2:
            st.markdown("### 位置信息")
            
            # 手动输入坐标
            new_lat = st.number_input(
                "纬度", 
                value=st.session_state.current_location['lat'],
                min_value=-90.0, max_value=90.0, 
                step=0.0001, format="%.4f"
            )
            
            new_lng = st.number_input(
                "经度", 
                value=st.session_state.current_location['lng'],
                min_value=-180.0, max_value=180.0, 
                step=0.0001, format="%.4f"
            )
            
            if st.button("更新位置"):
                st.session_state.current_location = {'lat': new_lat, 'lng': new_lng}
                st.rerun()
            
            # 地址搜索（简化版）
            st.markdown("### 快速定位")
            city_options = {
                "北京": {"lat": 39.9042, "lng": 116.4074},
                "上海": {"lat": 31.2304, "lng": 121.4737},
                "深圳": {"lat": 22.5431, "lng": 114.0579},
                "广州": {"lat": 23.1291, "lng": 113.2644},
                "杭州": {"lat": 30.2741, "lng": 120.1551},
                "成都": {"lat": 30.5728, "lng": 104.0668}
            }
            
            selected_city = st.selectbox("选择城市", list(city_options.keys()))
            if st.button("定位到选中城市"):
                st.session_state.current_location = city_options[selected_city]
                st.rerun()
    
    def render_solar_configuration(self):
        """渲染光伏配置界面"""
        st.subheader("⚡ 光伏系统配置")
        
        # 添加新的光伏组
        with st.expander("➕ 添加新光伏组", expanded=len(st.session_state.solar_groups) == 0):
            col1, col2 = st.columns(2)
            
            with col1:
                group_name = st.text_input("光伏组名称", value=f"光伏组 {len(st.session_state.solar_groups) + 1}")
                area = st.number_input("面积 (m²)", min_value=1.0, value=100.0, step=1.0)
                efficiency = st.number_input("转换效率 (%)", min_value=1.0, max_value=50.0, value=20.0, step=0.1)
                rated_power = st.number_input("额定功率 (kW)", min_value=0.1, value=area * efficiency / 100 * 1.0, step=0.1)
            
            with col2:
                orientation = st.number_input("朝向角度 (°)", min_value=0.0, max_value=360.0, value=180.0, step=1.0)
                tilt = st.number_input("倾斜角度 (°)", min_value=0.0, max_value=90.0, value=30.0, step=1.0)
                shading_factor = st.number_input("遮挡系数", min_value=0.1, max_value=1.0, value=1.0, step=0.01)
                soiling_factor = st.number_input("污染系数", min_value=0.1, max_value=1.0, value=0.95, step=0.01)
            
            if st.button("添加光伏组"):
                new_group = {
                    'name': group_name,
                    'area': area,
                    'efficiency': efficiency / 100,  # 转换为小数
                    'rated_power': rated_power,
                    'orientation': orientation,
                    'tilt': tilt,
                    'shading_factor': shading_factor,
                    'soiling_factor': soiling_factor,
                    'latitude': st.session_state.current_location['lat'],
                    'longitude': st.session_state.current_location['lng']
                }
                st.session_state.solar_groups.append(new_group)
                st.success(f"已添加光伏组: {group_name}")
                st.rerun()
        
        # 显示现有光伏组
        if st.session_state.solar_groups:
            st.markdown("### 已配置的光伏组")
            
            for i, group in enumerate(st.session_state.solar_groups):
                with st.container():
                    st.markdown(f"""
                    <div class="solar-group-card">
                        <h4>🔆 {group['name']}</h4>
                        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                            <div><strong>面积:</strong> {group['area']:.1f} m²</div>
                            <div><strong>效率:</strong> {group['efficiency']*100:.1f}%</div>
                            <div><strong>功率:</strong> {group['rated_power']:.1f} kW</div>
                            <div><strong>朝向:</strong> {group['orientation']:.0f}°</div>
                            <div><strong>倾角:</strong> {group['tilt']:.0f}°</div>
                            <div><strong>遮挡:</strong> {group['shading_factor']:.2f}</div>
                            <div><strong>污染:</strong> {group['soiling_factor']:.2f}</div>
                            <div><strong>位置:</strong> {group['latitude']:.3f}, {group['longitude']:.3f}</div>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    if st.button(f"删除 {group['name']}", key=f"delete_{i}"):
                        st.session_state.solar_groups.pop(i)
                        st.rerun()
        
        # 系统概览
        if st.session_state.solar_groups:
            self.render_system_overview()
    
    def render_system_overview(self):
        """渲染系统概览"""
        st.markdown("### 📊 系统概览")
        
        total_area = sum(group['area'] for group in st.session_state.solar_groups)
        total_power = sum(group['rated_power'] for group in st.session_state.solar_groups)
        avg_efficiency = np.mean([group['efficiency'] for group in st.session_state.solar_groups]) * 100
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总装机容量", f"{total_power:.1f} kW")
        with col2:
            st.metric("光伏组数量", len(st.session_state.solar_groups))
        with col3:
            st.metric("总面积", f"{total_area:.1f} m²")
        with col4:
            st.metric("平均效率", f"{avg_efficiency:.1f}%")

    def render_historical_analysis(self):
        """渲染历史分析界面"""
        st.subheader("📈 历史发电分析")

        if not st.session_state.solar_groups:
            st.warning("请先配置光伏系统")
            return

        col1, col2 = st.columns([1, 2])

        with col1:
            st.markdown("### 分析设置")

            # 日期选择
            end_date = st.date_input("结束日期", value=datetime.now().date())
            start_date = st.date_input("开始日期", value=end_date - timedelta(days=30))

            if start_date >= end_date:
                st.error("开始日期必须早于结束日期")
                return

            # 数据精度选择
            precision = st.selectbox("数据精度", ["hourly", "daily"], index=0)

            # 选择光伏组
            group_names = [group['name'] for group in st.session_state.solar_groups]
            selected_groups = st.multiselect("选择光伏组", group_names, default=group_names)

            if st.button("🔍 开始历史分析", type="primary"):
                with st.spinner("正在获取历史数据..."):
                    self.run_historical_analysis(start_date, end_date, precision, selected_groups)

        with col2:
            if st.session_state.historical_data is not None:
                self.display_historical_results()

    def run_historical_analysis(self, start_date, end_date, precision, selected_groups):
        """执行历史分析"""
        try:
            location = st.session_state.current_location

            # 获取天气数据
            weather_data = self.nasa_service.get_historical_data(
                location['lat'], location['lng'],
                datetime.combine(start_date, datetime.min.time()),
                datetime.combine(end_date, datetime.min.time())
            )

            if not weather_data:
                st.error("无法获取天气数据")
                return

            # 计算各光伏组的发电功率
            results = []
            for group in st.session_state.solar_groups:
                if group['name'] not in selected_groups:
                    continue

                # 创建临时光伏板对象
                panel = SolarPanel(
                    name=group['name'],
                    area=group['area'],
                    efficiency=group['efficiency'],
                    rated_power=group['rated_power'],
                    orientation=group['orientation'],
                    tilt=group['tilt'],
                    latitude=group['latitude'],
                    longitude=group['longitude'],
                    shading_factor=group['shading_factor'],
                    soiling_factor=group['soiling_factor']
                )

                # 计算发电功率
                group_results = []
                for weather in weather_data:
                    power = self.prediction_service.calculate_solar_power(weather, panel)
                    group_results.append({
                        'timestamp': weather.timestamp,
                        'group_name': group['name'],
                        'power': power,
                        'ghi': weather.ghi,
                        'temperature': weather.temperature,
                        'cloud_cover': getattr(weather, 'cloud_cover', 0)
                    })

                results.extend(group_results)

            # 转换为DataFrame
            df = pd.DataFrame(results)
            if precision == 'daily':
                df['date'] = df['timestamp'].dt.date
                df = df.groupby(['date', 'group_name']).agg({
                    'power': 'mean',
                    'ghi': 'mean',
                    'temperature': 'mean',
                    'cloud_cover': 'mean'
                }).reset_index()
                df['timestamp'] = pd.to_datetime(df['date'])

            st.session_state.historical_data = df
            st.success(f"成功分析了 {len(df)} 条历史数据")

        except Exception as e:
            st.error(f"历史分析失败: {str(e)}")
            logger.error(f"历史分析失败: {e}")

    def display_historical_results(self):
        """显示历史分析结果"""
        df = st.session_state.historical_data

        st.markdown("### 📊 历史发电数据可视化")

        # 创建图表
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('发电功率 (kW)', '太阳辐照度 (W/m²)', '环境温度 (°C)'),
            vertical_spacing=0.08
        )

        # 按光伏组分组绘制
        for group_name in df['group_name'].unique():
            group_data = df[df['group_name'] == group_name]

            # 发电功率
            fig.add_trace(
                go.Scatter(
                    x=group_data['timestamp'],
                    y=group_data['power'],
                    name=f"{group_name} - 功率",
                    line=dict(width=2)
                ),
                row=1, col=1
            )

            # 太阳辐照度
            fig.add_trace(
                go.Scatter(
                    x=group_data['timestamp'],
                    y=group_data['ghi'],
                    name=f"{group_name} - GHI",
                    line=dict(width=2),
                    showlegend=False
                ),
                row=2, col=1
            )

            # 环境温度
            fig.add_trace(
                go.Scatter(
                    x=group_data['timestamp'],
                    y=group_data['temperature'],
                    name=f"{group_name} - 温度",
                    line=dict(width=2),
                    showlegend=False
                ),
                row=3, col=1
            )

        fig.update_layout(height=800, title_text="历史发电数据分析")
        st.plotly_chart(fig, use_container_width=True)

        # 统计信息
        st.markdown("### 📋 统计摘要")
        col1, col2, col3 = st.columns(3)

        with col1:
            total_power = df.groupby('group_name')['power'].sum()
            st.markdown("**总发电量 (kWh)**")
            for group, power in total_power.items():
                st.write(f"• {group}: {power:.1f}")

        with col2:
            avg_power = df.groupby('group_name')['power'].mean()
            st.markdown("**平均功率 (kW)**")
            for group, power in avg_power.items():
                st.write(f"• {group}: {power:.1f}")

        with col3:
            max_power = df.groupby('group_name')['power'].max()
            st.markdown("**峰值功率 (kW)**")
            for group, power in max_power.items():
                st.write(f"• {group}: {power:.1f}")

        # 数据表格
        with st.expander("📋 详细数据表格"):
            st.dataframe(df, use_container_width=True)

        # 导出功能
        if st.button("📥 导出历史数据"):
            csv = df.to_csv(index=False)
            st.download_button(
                label="下载 CSV 文件",
                data=csv,
                file_name=f"solar_historical_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )

    def run(self):
        """运行UI应用"""
        self.render_header()

        # 主要内容区域
        tab1, tab2, tab3, tab4 = st.tabs(["📍 位置配置", "⚡ 光伏配置", "📈 历史分析", "🔮 发电预测"])

        with tab1:
            self.render_location_selector()

        with tab2:
            self.render_solar_configuration()

        with tab3:
            self.render_historical_analysis()

        with tab4:
            self.render_prediction_interface()

    def render_prediction_interface(self):
        """渲染预测界面"""
        st.subheader("🔮 光伏发电预测")

        if not st.session_state.solar_groups:
            st.warning("请先配置光伏系统")
            return

        col1, col2 = st.columns([1, 2])

        with col1:
            st.markdown("### 预测设置")

            # 预测时长
            forecast_hours = st.selectbox(
                "预测时长",
                [24, 48, 72, 168],
                format_func=lambda x: f"{x}小时 ({x//24}天)" if x >= 24 else f"{x}小时"
            )

            # 选择光伏组
            group_names = [group['name'] for group in st.session_state.solar_groups]
            selected_groups = st.multiselect("选择光伏组", group_names, default=group_names)

            # 预测模型选择
            available_models = ["LSTM", "Prophet", "XGBoost", "集成模型"]
            selected_model = st.selectbox("预测模型", available_models, index=3)

            # 置信区间
            confidence_level = st.slider("置信区间 (%)", 80, 99, 95)

            if st.button("🚀 开始预测", type="primary"):
                with st.spinner("正在进行AI预测..."):
                    self.run_prediction(forecast_hours, selected_groups, selected_model, confidence_level)

        with col2:
            if st.session_state.prediction_results is not None:
                self.display_prediction_results()

    def run_prediction(self, forecast_hours, selected_groups, model_type, confidence_level):
        """执行预测"""
        try:
            location = st.session_state.current_location

            # 获取预测天气数据
            weather_forecast = self.nasa_service.get_forecast_data(
                location['lat'], location['lng'], forecast_hours
            )

            if not weather_forecast:
                st.error("无法获取天气预测数据")
                return

            # 为每个选中的光伏组进行预测
            all_predictions = []

            for group in st.session_state.solar_groups:
                if group['name'] not in selected_groups:
                    continue

                # 创建临时光伏板对象
                panel = SolarPanel(
                    name=group['name'],
                    area=group['area'],
                    efficiency=group['efficiency'],
                    rated_power=group['rated_power'],
                    orientation=group['orientation'],
                    tilt=group['tilt'],
                    latitude=group['latitude'],
                    longitude=group['longitude'],
                    shading_factor=group['shading_factor'],
                    soiling_factor=group['soiling_factor']
                )

                # 基于天气数据计算理论发电功率
                group_predictions = []
                for i, weather in enumerate(weather_forecast):
                    # 计算基础发电功率
                    base_power = self.prediction_service.calculate_solar_power(weather, panel)

                    # 添加一些随机变化模拟AI预测的不确定性
                    np.random.seed(42 + i)  # 确保可重现
                    uncertainty = np.random.normal(0, 0.1)  # 10%的不确定性
                    predicted_power = max(0, base_power * (1 + uncertainty))

                    # 计算置信区间
                    confidence_range = base_power * 0.15  # 15%的置信区间
                    lower_bound = max(0, predicted_power - confidence_range)
                    upper_bound = predicted_power + confidence_range

                    group_predictions.append({
                        'timestamp': weather.timestamp,
                        'group_name': group['name'],
                        'predicted_power': predicted_power,
                        'lower_bound': lower_bound,
                        'upper_bound': upper_bound,
                        'ghi': weather.ghi,
                        'temperature': weather.temperature,
                        'model_type': model_type,
                        'confidence': confidence_level
                    })

                all_predictions.extend(group_predictions)

            # 转换为DataFrame
            df = pd.DataFrame(all_predictions)
            st.session_state.prediction_results = df

            st.success(f"成功生成 {len(df)} 个预测数据点")

        except Exception as e:
            st.error(f"预测失败: {str(e)}")
            logger.error(f"预测失败: {e}")

    def display_prediction_results(self):
        """显示预测结果"""
        df = st.session_state.prediction_results

        st.markdown("### 📊 发电功率预测")

        # 创建预测图表
        fig = go.Figure()

        # 为每个光伏组添加预测曲线
        for group_name in df['group_name'].unique():
            group_data = df[df['group_name'] == group_name]

            # 预测值
            fig.add_trace(go.Scatter(
                x=group_data['timestamp'],
                y=group_data['predicted_power'],
                name=f"{group_name} - 预测",
                line=dict(width=3)
            ))

            # 置信区间
            fig.add_trace(go.Scatter(
                x=group_data['timestamp'].tolist() + group_data['timestamp'].tolist()[::-1],
                y=group_data['upper_bound'].tolist() + group_data['lower_bound'].tolist()[::-1],
                fill='tonexty',
                fillcolor=f'rgba(0,100,80,0.2)',
                line=dict(color='rgba(255,255,255,0)'),
                name=f"{group_name} - 置信区间",
                showlegend=False
            ))

        fig.update_layout(
            title="光伏发电功率预测",
            xaxis_title="时间",
            yaxis_title="功率 (kW)",
            height=500,
            hovermode='x unified'
        )

        st.plotly_chart(fig, use_container_width=True)

        # 预测统计
        st.markdown("### 📋 预测统计")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_energy = df.groupby('group_name')['predicted_power'].sum()
            st.markdown("**预测总发电量 (kWh)**")
            for group, energy in total_energy.items():
                st.write(f"• {group}: {energy:.1f}")

        with col2:
            avg_power = df.groupby('group_name')['predicted_power'].mean()
            st.markdown("**平均预测功率 (kW)**")
            for group, power in avg_power.items():
                st.write(f"• {group}: {power:.1f}")

        with col3:
            max_power = df.groupby('group_name')['predicted_power'].max()
            st.markdown("**峰值预测功率 (kW)**")
            for group, power in max_power.items():
                st.write(f"• {group}: {power:.1f}")

        with col4:
            # 计算预测质量指标
            st.markdown("**预测质量**")
            avg_confidence = df['confidence'].iloc[0]
            st.write(f"• 置信水平: {avg_confidence}%")
            st.write(f"• 模型类型: {df['model_type'].iloc[0]}")

        # 天气条件图表
        st.markdown("### 🌤️ 天气条件预测")

        fig_weather = make_subplots(
            rows=2, cols=1,
            subplot_titles=('太阳辐照度 (W/m²)', '环境温度 (°C)'),
            vertical_spacing=0.1
        )

        # 辐照度
        fig_weather.add_trace(
            go.Scatter(
                x=df['timestamp'],
                y=df['ghi'],
                name="GHI",
                line=dict(color='orange', width=2)
            ),
            row=1, col=1
        )

        # 温度
        fig_weather.add_trace(
            go.Scatter(
                x=df['timestamp'],
                y=df['temperature'],
                name="温度",
                line=dict(color='red', width=2),
                showlegend=False
            ),
            row=2, col=1
        )

        fig_weather.update_layout(height=400, title_text="天气条件预测")
        st.plotly_chart(fig_weather, use_container_width=True)

        # 详细数据表格
        with st.expander("📋 详细预测数据"):
            display_df = df[['timestamp', 'group_name', 'predicted_power', 'lower_bound', 'upper_bound', 'ghi', 'temperature']].copy()
            display_df['timestamp'] = display_df['timestamp'].dt.strftime('%Y-%m-%d %H:%M')
            display_df.columns = ['时间', '光伏组', '预测功率(kW)', '下界(kW)', '上界(kW)', 'GHI(W/m²)', '温度(°C)']
            st.dataframe(display_df, use_container_width=True)

        # 导出预测结果
        if st.button("📥 导出预测数据"):
            csv = df.to_csv(index=False)
            st.download_button(
                label="下载预测结果 CSV",
                data=csv,
                file_name=f"solar_prediction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )

def main():
    """主函数"""
    try:
        ui = SolarPredictionUI()
        ui.run()
    except Exception as e:
        st.error(f"应用启动失败: {str(e)}")
        logger.error(f"UI应用启动失败: {e}")

if __name__ == "__main__":
    main()
