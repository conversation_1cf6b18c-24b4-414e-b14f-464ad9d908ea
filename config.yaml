# VPP-AI 新能源微网决策系统配置文件
# =====================================

# 系统基本配置
system:
  name: "VPP-AI"
  version: "1.0.0"
  description: "新能源微网决策系统"
  timezone: "Asia/Shanghai"
  debug: true

# 数据库配置
database:
  # PostgreSQL主数据库
  postgres:
    host: "localhost"
    port: 5432
    database: "vpp_ai"
    username: "vpp_user"
    password: "vpp_password"
    
  # Redis缓存
  redis:
    host: "localhost"
    port: 6379
    database: 0
    password: ""
    
  # MongoDB时序数据
  mongodb:
    host: "localhost"
    port: 27017
    database: "vpp_timeseries"

# API服务配置
api:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  reload: true

# NASA POWER API 配置
nasa_power:
  base_url: "https://power.larc.nasa.gov/api/temporal/hourly/point"
  community: "RE"  # Renewable Energy
  time_standard: "LST"  # Local Solar Time (或 UTC)
  format: "JSON"

  # 请求参数 (使用NASA POWER API支持的参数)
  parameters:
    - "ALLSKY_SFC_SW_DWN"        # 全天候地表短波辐照度 (GHI)
    - "T2M"                      # 2米高度气温
    - "RH2M"                     # 2米高度相对湿度
    - "WS10M"                    # 10米高度风速

  # 缓存配置
  cache_enabled: true
  cache_ttl: 3600  # 缓存时间(秒)

  # 请求限制
  max_retries: 3
  timeout: 30
  rate_limit: 60  # 每分钟最大请求数

# 光伏系统配置
solar:
  # 光伏板参数
  panel_area: 1000.0  # 平方米
  panel_efficiency: 0.22  # 效率22%
  panel_orientation: 180  # 朝向角度(南向)
  panel_tilt: 30  # 倾斜角度

  # 逆变器参数
  inverter_efficiency: 0.95
  inverter_capacity: 200.0  # kW

  # 地理位置(默认位置)
  latitude: 39.9042          # 纬度(北京)
  longitude: 116.4074        # 经度(北京)
  altitude: 43.5             # 海拔(米)

  # 环境参数
  shading_factor: 1.0        # 遮挡系数
  soiling_factor: 0.95       # 污染系数
  temperature_coefficient: -0.004  # 温度系数

  # 预测模型配置
  prediction:
    models:
      - name: "lstm"
        enabled: true
        weight: 0.4
        params:
          sequence_length: 24
          hidden_units: 50
          epochs: 100
          batch_size: 32

      - name: "prophet"
        enabled: true
        weight: 0.3
        params:
          seasonality_mode: "multiplicative"
          yearly_seasonality: true
          weekly_seasonality: true
          daily_seasonality: true

      - name: "xgboost"
        enabled: true
        weight: 0.3
        params:
          n_estimators: 100
          max_depth: 6
          learning_rate: 0.1

    # 预测配置
    forecast_horizon: 24       # 预测时长(小时)
    update_interval: 3600      # 更新间隔(秒)
    model_retrain_days: 30     # 模型重训练周期(天)
    confidence_level: 0.95     # 置信水平

    # 数据处理
    min_data_points: 168       # 最少数据点数(7天)
    outlier_threshold: 3.0     # 异常值检测阈值(标准差倍数)
    smoothing_window: 3        # 数据平滑窗口

# 储能系统配置
storage:
  # 电池参数
  capacity: 500.0  # kWh
  max_charge_power: 100.0  # kW
  max_discharge_power: 100.0  # kW
  charge_efficiency: 0.95
  discharge_efficiency: 0.95
  min_soc: 0.1  # 最小荷电状态
  max_soc: 0.9  # 最大荷电状态
  
  # 电池寿命参数
  cycle_life: 6000
  calendar_life: 15  # 年

# 负荷配置
load:
  # 负荷类型
  types:
    - name: "空调"
      priority: 2
      flexibility: 0.3
    - name: "照明"
      priority: 1
      flexibility: 0.1
    - name: "生产设备"
      priority: 3
      flexibility: 0.0
    - name: "电动汽车充电"
      priority: 2
      flexibility: 0.8

# 电网配置
grid:
  # 电价配置(元/kWh)
  electricity_price:
    peak: 1.2      # 峰时电价
    flat: 0.8      # 平时电价
    valley: 0.4    # 谷时电价
    
  # 时段配置
  time_periods:
    peak: ["09:00-12:00", "18:00-22:00"]
    valley: ["23:00-07:00"]
    
  # 上网电价(元/kWh)
  feed_in_tariff: 0.35

# AI模型配置
ai_models:
  # 光伏预测模型
  solar_forecast:
    model_type: "lstm"
    sequence_length: 24
    forecast_horizon: 48
    retrain_interval: 7  # 天
    
  # 负荷预测模型
  load_forecast:
    model_type: "prophet"
    forecast_horizon: 24
    retrain_interval: 7
    
  # 天气预测集成
  weather_api:
    provider: "openweathermap"
    api_key: "your_api_key_here"
    update_interval: 3600  # 秒

# 优化算法配置
optimization:
  # 优化目标
  objective: "maximize_profit"
  
  # 优化时间窗口
  horizon: 24  # 小时
  
  # 约束条件
  constraints:
    power_balance: true
    storage_limits: true
    grid_limits: true
    
  # 求解器
  solver: "ECOS"

# 数字孪生配置
digital_twin:
  # 仿真参数
  simulation_step: 300  # 秒
  real_time_factor: 1.0
  
  # 设备建模
  device_models:
    solar_panel: "detailed"
    battery: "equivalent_circuit"
    inverter: "efficiency_curve"
    load: "statistical"

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "1 day"
  retention: "30 days"

# 监控配置
monitoring:
  # 数据采集间隔
  data_collection_interval: 60  # 秒
  
  # 告警阈值
  alerts:
    battery_low_soc: 0.15
    high_temperature: 60  # 摄氏度
    grid_disconnection: true
    
# 安全配置
security:
  # API密钥
  api_key_length: 32

  # 数据加密
  encryption_enabled: true

  # 访问控制
  rate_limit: 1000  # 每小时请求数

# UI界面配置
ui:
  # 谷歌地图API配置
  google_maps:
    api_key: "YOUR_GOOGLE_MAPS_API_KEY"  # 请替换为您的API密钥
    libraries: ["places"]
    language: "zh-CN"
    region: "CN"

  # Streamlit配置
  streamlit:
    port: 8501
    host: "0.0.0.0"
    browser_gather_usage_stats: false
    theme:
      primary_color: "#FF6B35"
      background_color: "#FFFFFF"
      secondary_background_color: "#F0F2F6"
      text_color: "#262730"

  # Flask配置
  flask:
    port: 5000
    host: "0.0.0.0"
    debug: true
    threaded: true

  # 默认位置配置
  default_location:
    latitude: 39.9042
    longitude: 116.4074
    name: "北京"

  # 快速定位城市
  quick_locations:
    - name: "北京"
      latitude: 39.9042
      longitude: 116.4074
    - name: "上海"
      latitude: 31.2304
      longitude: 121.4737
    - name: "深圳"
      latitude: 22.5431
      longitude: 114.0579
    - name: "广州"
      latitude: 23.1291
      longitude: 113.2644
    - name: "杭州"
      latitude: 30.2741
      longitude: 120.1551
    - name: "成都"
      latitude: 30.5728
      longitude: 104.0668
