#!/usr/bin/env python3
"""
🌞 VPP-AI 光伏发电预测 UI 后端服务
=====================================

为前端UI提供API接口，连接现有的光伏预测服务
"""

from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import traceback
from typing import Dict, List, Any, Optional

# 导入项目模块
from src.services.prediction.solar_prediction_service import SolarPredictionService
from src.services.prediction.nasa_power_service import NASAPowerService
from src.models.solar import SolarPanel
from src.core.config import config
from src.core.logger import get_logger

# 初始化Flask应用
app = Flask(__name__, 
           template_folder='web_ui',
           static_folder='web_ui')
CORS(app)

# 初始化日志
logger = get_logger(__name__)

# 初始化服务
try:
    nasa_service = NASAPowerService()
    prediction_service = SolarPredictionService()
    logger.info("后端服务初始化成功")
except Exception as e:
    logger.error(f"后端服务初始化失败: {e}")
    nasa_service = None
    prediction_service = None

class SolarUIBackend:
    """光伏UI后端服务类"""
    
    def __init__(self):
        self.nasa_service = nasa_service
        self.prediction_service = prediction_service
    
    def create_solar_panel_from_config(self, config: Dict) -> SolarPanel:
        """从配置创建光伏板对象"""
        return SolarPanel(
            name=config.get('name', 'Solar Panel'),
            area=config.get('area', 100.0),
            efficiency=config.get('efficiency', 0.2),
            rated_power=config.get('rated_power', 20.0),
            orientation=config.get('orientation', 180.0),
            tilt=config.get('tilt', 30.0),
            latitude=config.get('latitude', 39.9042),
            longitude=config.get('longitude', 116.4074),
            shading_factor=config.get('shading_factor', 1.0),
            soiling_factor=config.get('soiling_factor', 0.95),
            temperature_coefficient=config.get('temperature_coefficient', -0.004)
        )
    
    def get_historical_analysis(self, location: Dict, solar_groups: List[Dict], 
                              start_date: str, end_date: str, precision: str = 'hourly') -> Dict:
        """获取历史分析数据"""
        try:
            if not self.nasa_service:
                raise Exception("NASA服务未初始化")
            
            # 解析日期
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            # 获取天气数据
            weather_data = self.nasa_service.get_historical_data(
                location['lat'], location['lng'], start_dt, end_dt
            )
            
            if not weather_data:
                raise Exception("无法获取天气数据")
            
            # 计算各光伏组的发电功率
            results = []
            for group_config in solar_groups:
                panel = self.create_solar_panel_from_config(group_config)
                
                for weather in weather_data:
                    power = self.prediction_service.calculate_solar_power(weather, panel)
                    results.append({
                        'timestamp': weather.timestamp.isoformat(),
                        'group_name': group_config['name'],
                        'power': round(power, 2),
                        'ghi': round(weather.ghi, 1),
                        'temperature': round(weather.temperature, 1),
                        'cloud_cover': getattr(weather, 'cloud_cover', 0)
                    })
            
            # 数据聚合（如果需要日级精度）
            if precision == 'daily':
                df = pd.DataFrame(results)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df['date'] = df['timestamp'].dt.date
                
                daily_results = []
                for (date, group), group_data in df.groupby(['date', 'group_name']):
                    daily_results.append({
                        'timestamp': date.isoformat(),
                        'group_name': group,
                        'power': round(group_data['power'].mean(), 2),
                        'ghi': round(group_data['ghi'].mean(), 1),
                        'temperature': round(group_data['temperature'].mean(), 1),
                        'cloud_cover': round(group_data['cloud_cover'].mean(), 1)
                    })
                results = daily_results
            
            return {
                'success': True,
                'data': results,
                'count': len(results),
                'message': f'成功获取 {len(results)} 条历史数据'
            }
            
        except Exception as e:
            logger.error(f"历史分析失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '历史分析失败'
            }
    
    def get_power_prediction(self, location: Dict, solar_groups: List[Dict], 
                           forecast_hours: int = 24, model_type: str = 'ensemble') -> Dict:
        """获取发电功率预测"""
        try:
            if not self.nasa_service:
                raise Exception("NASA服务未初始化")
            
            # 获取预测天气数据
            weather_forecast = self.nasa_service.get_forecast_data(
                location['lat'], location['lng'], forecast_hours
            )
            
            if not weather_forecast:
                raise Exception("无法获取天气预测数据")
            
            # 为每个光伏组生成预测
            predictions = []
            for group_config in solar_groups:
                panel = self.create_solar_panel_from_config(group_config)
                
                for i, weather in enumerate(weather_forecast):
                    # 计算基础发电功率
                    base_power = self.prediction_service.calculate_solar_power(weather, panel)
                    
                    # 添加预测不确定性
                    np.random.seed(42 + i)  # 确保可重现
                    uncertainty = np.random.normal(0, 0.1)  # 10%的不确定性
                    predicted_power = max(0, base_power * (1 + uncertainty))
                    
                    # 计算置信区间
                    confidence_range = base_power * 0.15  # 15%的置信区间
                    lower_bound = max(0, predicted_power - confidence_range)
                    upper_bound = predicted_power + confidence_range
                    
                    predictions.append({
                        'timestamp': weather.timestamp.isoformat(),
                        'group_name': group_config['name'],
                        'predicted_power': round(predicted_power, 2),
                        'lower_bound': round(lower_bound, 2),
                        'upper_bound': round(upper_bound, 2),
                        'ghi': round(weather.ghi, 1),
                        'temperature': round(weather.temperature, 1),
                        'model_type': model_type,
                        'confidence': 95
                    })
            
            return {
                'success': True,
                'data': predictions,
                'count': len(predictions),
                'message': f'成功生成 {len(predictions)} 个预测数据点'
            }
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '预测失败'
            }

# 初始化后端服务
backend = SolarUIBackend()

# 路由定义
@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/status')
def api_status():
    """API状态检查"""
    status = {
        'nasa_service': nasa_service is not None,
        'prediction_service': prediction_service is not None,
        'timestamp': datetime.now().isoformat()
    }
    return jsonify(status)

@app.route('/api/historical-analysis', methods=['POST'])
def historical_analysis():
    """历史分析API"""
    try:
        data = request.get_json()
        
        location = data.get('location', {})
        solar_groups = data.get('solar_groups', [])
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        precision = data.get('precision', 'hourly')
        
        if not location or not solar_groups:
            return jsonify({
                'success': False,
                'error': '缺少必要参数',
                'message': '请提供位置和光伏配置信息'
            }), 400
        
        result = backend.get_historical_analysis(
            location, solar_groups, start_date, end_date, precision
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"历史分析API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '服务器内部错误'
        }), 500

@app.route('/api/power-prediction', methods=['POST'])
def power_prediction():
    """发电预测API"""
    try:
        data = request.get_json()
        
        location = data.get('location', {})
        solar_groups = data.get('solar_groups', [])
        forecast_hours = data.get('forecast_hours', 24)
        model_type = data.get('model_type', 'ensemble')
        
        if not location or not solar_groups:
            return jsonify({
                'success': False,
                'error': '缺少必要参数',
                'message': '请提供位置和光伏配置信息'
            }), 400
        
        result = backend.get_power_prediction(
            location, solar_groups, forecast_hours, model_type
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"预测API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '服务器内部错误'
        }), 500

@app.route('/api/weather-data', methods=['POST'])
def weather_data():
    """天气数据API"""
    try:
        data = request.get_json()
        location = data.get('location', {})
        
        if not location:
            return jsonify({
                'success': False,
                'error': '缺少位置信息'
            }), 400
        
        # 获取最近7天的天气数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        weather_data = nasa_service.get_historical_data(
            location['lat'], location['lng'], start_date, end_date
        )
        
        if weather_data:
            weather_list = []
            for weather in weather_data[-24:]:  # 最近24小时
                weather_list.append({
                    'timestamp': weather.timestamp.isoformat(),
                    'ghi': round(weather.ghi, 1),
                    'temperature': round(weather.temperature, 1),
                    'humidity': getattr(weather, 'humidity', 0),
                    'wind_speed': getattr(weather, 'wind_speed', 0)
                })
            
            return jsonify({
                'success': True,
                'data': weather_list,
                'message': '天气数据获取成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '无法获取天气数据'
            }), 500
            
    except Exception as e:
        logger.error(f"天气数据API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'success': False,
        'error': '接口不存在',
        'message': '请检查API路径'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        'success': False,
        'error': '服务器内部错误',
        'message': '请稍后重试'
    }), 500

if __name__ == '__main__':
    print("🌞 启动VPP-AI光伏预测UI后端服务...")
    print(f"📍 访问地址: http://localhost:5000")
    print(f"🔧 调试模式: 开启")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
