#!/usr/bin/env python3
"""
🌞 VPP-AI 光伏发电预测 UI 演示脚本
===================================

演示光伏预测UI的主要功能，包括：
1. 位置选择和配置
2. 光伏系统配置
3. 历史数据分析
4. 发电功率预测
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.services.prediction.solar_prediction_service import SolarPredictionService
    from src.services.prediction.nasa_power_service import NASAPowerService
    from src.models.solar import SolarPanel
    from src.core.config import config
    from src.core.logger import get_logger
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)

# 初始化日志
logger = get_logger(__name__)

class SolarUIDemo:
    """光伏UI演示类"""
    
    def __init__(self):
        """初始化演示环境"""
        print("🌞 VPP-AI 光伏发电预测 UI 演示")
        print("=" * 50)
        
        self.nasa_service = None
        self.prediction_service = None
        
        # 演示配置
        self.demo_location = {"lat": 39.9042, "lng": 116.4074}  # 北京
        self.demo_solar_groups = [
            {
                "name": "主光伏组A",
                "area": 150.0,
                "efficiency": 0.22,
                "rated_power": 33.0,
                "orientation": 180.0,
                "tilt": 30.0,
                "shading_factor": 1.0,
                "soiling_factor": 0.95,
                "latitude": 39.9042,
                "longitude": 116.4074
            },
            {
                "name": "辅助光伏组B",
                "area": 100.0,
                "efficiency": 0.20,
                "rated_power": 20.0,
                "orientation": 160.0,
                "tilt": 25.0,
                "shading_factor": 0.9,
                "soiling_factor": 0.93,
                "latitude": 39.9042,
                "longitude": 116.4074
            }
        ]
        
        self._init_services()
    
    def _init_services(self):
        """初始化服务"""
        try:
            print("🔧 初始化服务...")
            self.nasa_service = NASAPowerService()
            self.prediction_service = SolarPredictionService()
            print("✅ 服务初始化成功")
        except Exception as e:
            print(f"❌ 服务初始化失败: {e}")
            logger.error(f"服务初始化失败: {e}")
    
    def demo_location_selection(self):
        """演示位置选择功能"""
        print("\n📍 演示1: 位置选择功能")
        print("-" * 30)
        
        print(f"当前位置: {self.demo_location}")
        print("✅ 支持的位置选择方式:")
        print("  • 地图点击选择")
        print("  • 地址搜索")
        print("  • GPS定位")
        print("  • 手动输入坐标")
        
        # 模拟位置验证
        lat, lng = self.demo_location["lat"], self.demo_location["lng"]
        if -90 <= lat <= 90 and -180 <= lng <= 180:
            print(f"✅ 位置坐标有效: ({lat:.4f}, {lng:.4f})")
        else:
            print(f"❌ 位置坐标无效: ({lat:.4f}, {lng:.4f})")
    
    def demo_solar_configuration(self):
        """演示光伏配置功能"""
        print("\n⚡ 演示2: 光伏系统配置")
        print("-" * 30)
        
        total_area = 0
        total_power = 0
        
        for i, group in enumerate(self.demo_solar_groups, 1):
            print(f"\n🔆 光伏组 {i}: {group['name']}")
            print(f"  • 面积: {group['area']:.1f} m²")
            print(f"  • 效率: {group['efficiency']*100:.1f}%")
            print(f"  • 额定功率: {group['rated_power']:.1f} kW")
            print(f"  • 朝向: {group['orientation']:.0f}°")
            print(f"  • 倾角: {group['tilt']:.0f}°")
            print(f"  • 遮挡系数: {group['shading_factor']:.2f}")
            print(f"  • 污染系数: {group['soiling_factor']:.2f}")
            
            total_area += group['area']
            total_power += group['rated_power']
        
        print(f"\n📊 系统概览:")
        print(f"  • 总面积: {total_area:.1f} m²")
        print(f"  • 总装机容量: {total_power:.1f} kW")
        print(f"  • 光伏组数量: {len(self.demo_solar_groups)}")
        print(f"  • 平均效率: {sum(g['efficiency'] for g in self.demo_solar_groups)/len(self.demo_solar_groups)*100:.1f}%")
    
    def demo_historical_analysis(self):
        """演示历史分析功能"""
        print("\n📈 演示3: 历史发电分析")
        print("-" * 30)
        
        if not self.nasa_service:
            print("❌ NASA服务未初始化，跳过历史分析演示")
            return
        
        try:
            # 设置分析时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            print(f"📅 分析时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            print("🌐 正在获取NASA POWER天气数据...")
            
            # 获取天气数据
            weather_data = self.nasa_service.get_historical_data(
                self.demo_location['lat'], 
                self.demo_location['lng'], 
                start_date, 
                end_date
            )
            
            if weather_data:
                print(f"✅ 成功获取 {len(weather_data)} 条天气数据")
                
                # 显示数据样例
                print("\n🌤️ 天气数据样例 (最近3条):")
                for i, weather in enumerate(weather_data[-3:], 1):
                    print(f"  {i}. {weather.timestamp.strftime('%Y-%m-%d %H:%M')}")
                    print(f"     GHI: {weather.ghi:.1f} W/m², 温度: {weather.temperature:.1f}°C")
                
                # 计算发电功率
                print("\n⚡ 计算各光伏组发电功率...")
                total_power_data = []
                
                for group_config in self.demo_solar_groups:
                    # 创建光伏板对象
                    panel = SolarPanel(
                        name=group_config['name'],
                        area=group_config['area'],
                        efficiency=group_config['efficiency'],
                        rated_power=group_config['rated_power'],
                        orientation=group_config['orientation'],
                        tilt=group_config['tilt'],
                        latitude=group_config['latitude'],
                        longitude=group_config['longitude'],
                        shading_factor=group_config['shading_factor'],
                        soiling_factor=group_config['soiling_factor']
                    )
                    
                    # 计算发电功率
                    group_powers = []
                    for weather in weather_data[-24:]:  # 最近24小时
                        power = self.prediction_service.calculate_solar_power(weather, panel)
                        group_powers.append(power)
                    
                    avg_power = sum(group_powers) / len(group_powers)
                    max_power = max(group_powers)
                    total_energy = sum(group_powers)  # 简化计算
                    
                    print(f"  📊 {group_config['name']}:")
                    print(f"     平均功率: {avg_power:.2f} kW")
                    print(f"     峰值功率: {max_power:.2f} kW")
                    print(f"     总发电量: {total_energy:.1f} kWh")
                    
                    total_power_data.extend(group_powers)
                
                # 系统总体统计
                system_avg = sum(total_power_data) / len(total_power_data)
                system_max = max(total_power_data)
                print(f"\n🏭 系统总体表现:")
                print(f"  • 平均总功率: {system_avg:.2f} kW")
                print(f"  • 峰值总功率: {system_max:.2f} kW")
                print(f"  • 容量因子: {(system_avg/sum(g['rated_power'] for g in self.demo_solar_groups))*100:.1f}%")
                
            else:
                print("❌ 未获取到天气数据")
                
        except Exception as e:
            print(f"❌ 历史分析演示失败: {e}")
            logger.error(f"历史分析演示失败: {e}")
    
    def demo_power_prediction(self):
        """演示发电预测功能"""
        print("\n🔮 演示4: 发电功率预测")
        print("-" * 30)
        
        if not self.nasa_service:
            print("❌ NASA服务未初始化，跳过预测演示")
            return
        
        try:
            forecast_hours = 24
            print(f"🕐 预测时长: {forecast_hours} 小时")
            print("🌐 正在获取天气预测数据...")
            
            # 获取预测天气数据
            weather_forecast = self.nasa_service.get_forecast_data(
                self.demo_location['lat'], 
                self.demo_location['lng'], 
                forecast_hours
            )
            
            if weather_forecast:
                print(f"✅ 成功获取 {len(weather_forecast)} 条预测数据")
                
                # 为每个光伏组生成预测
                print("\n🤖 AI预测结果:")
                
                for group_config in self.demo_solar_groups:
                    # 创建光伏板对象
                    panel = SolarPanel(
                        name=group_config['name'],
                        area=group_config['area'],
                        efficiency=group_config['efficiency'],
                        rated_power=group_config['rated_power'],
                        orientation=group_config['orientation'],
                        tilt=group_config['tilt'],
                        latitude=group_config['latitude'],
                        longitude=group_config['longitude'],
                        shading_factor=group_config['shading_factor'],
                        soiling_factor=group_config['soiling_factor']
                    )
                    
                    # 计算预测功率
                    predictions = []
                    for weather in weather_forecast:
                        base_power = self.prediction_service.calculate_solar_power(weather, panel)
                        # 添加一些随机变化模拟AI预测
                        import numpy as np
                        np.random.seed(42)
                        uncertainty = np.random.normal(0, 0.1)
                        predicted_power = max(0, base_power * (1 + uncertainty))
                        predictions.append(predicted_power)
                    
                    avg_predicted = sum(predictions) / len(predictions)
                    max_predicted = max(predictions)
                    total_predicted = sum(predictions)
                    
                    print(f"  🔆 {group_config['name']}:")
                    print(f"     预测平均功率: {avg_predicted:.2f} kW")
                    print(f"     预测峰值功率: {max_predicted:.2f} kW")
                    print(f"     预测总发电量: {total_predicted:.1f} kWh")
                    print(f"     置信区间: ±15%")
                
                # 显示预测数据样例
                print(f"\n📊 预测数据样例 (前3小时):")
                for i, weather in enumerate(weather_forecast[:3], 1):
                    print(f"  {i}. {weather.timestamp.strftime('%Y-%m-%d %H:%M')}")
                    print(f"     预测GHI: {weather.ghi:.1f} W/m², 预测温度: {weather.temperature:.1f}°C")
                
            else:
                print("❌ 未获取到预测数据")
                
        except Exception as e:
            print(f"❌ 预测演示失败: {e}")
            logger.error(f"预测演示失败: {e}")
    
    def demo_ui_features(self):
        """演示UI特性"""
        print("\n🖥️ 演示5: UI界面特性")
        print("-" * 30)
        
        print("✅ 支持的UI功能:")
        print("  📱 响应式设计 - 适配不同屏幕尺寸")
        print("  🗺️ 交互式地图 - 谷歌地图集成")
        print("  📊 动态图表 - Plotly交互式图表")
        print("  📋 数据表格 - 详细数据展示")
        print("  💾 数据导出 - CSV格式导出")
        print("  🔄 实时更新 - 配置变更实时反映")
        print("  🎨 美观界面 - 现代化UI设计")
        print("  🚀 快速响应 - 优化的性能表现")
        
        print("\n🌐 访问方式:")
        print("  • Streamlit UI: http://localhost:8501")
        print("  • Flask Web UI: http://localhost:5000")
        
        print("\n📱 浏览器兼容性:")
        print("  ✅ Chrome 80+")
        print("  ✅ Firefox 75+")
        print("  ✅ Safari 13+")
        print("  ✅ Edge 80+")
    
    def run_demo(self):
        """运行完整演示"""
        try:
            self.demo_location_selection()
            time.sleep(1)
            
            self.demo_solar_configuration()
            time.sleep(1)
            
            self.demo_historical_analysis()
            time.sleep(1)
            
            self.demo_power_prediction()
            time.sleep(1)
            
            self.demo_ui_features()
            
            print("\n🎉 演示完成！")
            print("💡 运行 'python run_solar_ui.py' 启动完整UI系统")
            
        except KeyboardInterrupt:
            print("\n\n⏹️ 演示被用户中断")
        except Exception as e:
            print(f"\n❌ 演示过程中发生错误: {e}")
            logger.error(f"演示失败: {e}")

def main():
    """主函数"""
    demo = SolarUIDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()
