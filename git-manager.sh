#!/bin/bash

# GitHub仓库管理脚本
# 作者: <EMAIL>
# 版本: 2.0
# 用法: ./git-manager.sh [操作] [参数...]

# 默认配置
DEFAULT_LOG_COUNT=10
DEFAULT_BRANCH="main"
AUTO_PUSH=true
USE_COLORS=true
VERBOSE=true

# 加载配置文件
load_config() {
    local config_file=".git-manager.config"
    if [ -f "$config_file" ]; then
        source "$config_file"
        if [ "$VERBOSE" = true ]; then
            echo -e "${CYAN}已加载配置文件: $config_file${NC}"
        fi
    fi
}

# 颜色定义
if [ "$USE_COLORS" = true ]; then
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    PURPLE='\033[0;35m'
    CYAN='\033[0;36m'
    NC='\033[0m' # No Color
else
    RED=''
    GREEN=''
    YELLOW=''
    BLUE=''
    PURPLE=''
    CYAN=''
    NC=''
fi

# 显示时间戳
show_timestamp() {
    if [ "$SHOW_TIMESTAMPS" = true ]; then
        echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
    else
        echo -e "$1"
    fi
}

# 确认危险操作
confirm_operation() {
    if [ "$CONFIRM_DANGEROUS_OPERATIONS" = true ]; then
        echo -e "${YELLOW}确认执行此操作? (y/N):${NC}"
        read -r response
        case "$response" in
            [yY][eE][sS]|[yY])
                return 0
                ;;
            *)
                echo -e "${RED}操作已取消${NC}"
                exit 0
                ;;
        esac
    fi
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}GitHub仓库管理脚本${NC}"
    echo -e "${YELLOW}用法: ./git-manager.sh [操作] [参数...]${NC}"
    echo ""
    echo -e "${GREEN}基本操作:${NC}"
    echo "  status, s           - 查看仓库状态"
    echo "  add [文件]          - 添加文件到暂存区 (默认添加所有文件)"
    echo "  commit, c [消息]    - 提交变更"
    echo "  push, p [分支]      - 推送到远程仓库 (默认当前分支)"
    echo "  pull [分支]         - 从远程仓库拉取 (默认当前分支)"
    echo "  sync               - 同步：拉取+添加+提交+推送"
    echo ""
    echo -e "${GREEN}分支操作:${NC}"
    echo "  branch, b          - 列出所有分支"
    echo "  checkout, co [分支] - 切换分支"
    echo "  new-branch, nb [名称] - 创建并切换到新分支"
    echo "  delete-branch, db [分支] - 删除分支"
    echo "  merge [分支]       - 合并指定分支到当前分支"
    echo ""
    echo -e "${GREEN}历史和日志:${NC}"
    echo "  log, l [数量]      - 查看提交历史 (默认10条)"
    echo "  diff [文件]        - 查看文件差异"
    echo "  show [提交ID]      - 查看特定提交详情"
    echo ""
    echo -e "${GREEN}远程仓库:${NC}"
    echo "  remote             - 查看远程仓库信息"
    echo "  clone [URL] [目录] - 克隆仓库"
    echo "  fetch              - 获取远程更新"
    echo ""
    echo -e "${GREEN}标签操作:${NC}"
    echo "  tag [名称] [消息]  - 创建标签"
    echo "  tags               - 列出所有标签"
    echo "  push-tags          - 推送所有标签"
    echo ""
    echo -e "${GREEN}撤销操作:${NC}"
    echo "  reset [文件]       - 撤销暂存区文件"
    echo "  revert [提交ID]    - 撤销特定提交"
    echo "  clean              - 清理未跟踪文件"
    echo ""
    echo -e "${GREEN}快捷操作:${NC}"
    echo "  quick, q [消息]    - 快速提交推送 (add+commit+push)"
    echo "  backup             - 创建备份分支"
    echo "  release [版本]     - 创建发布标签并推送"
    echo ""
    echo -e "${GREEN}配置操作:${NC}"
    echo "  config             - 显示当前配置"
    echo "  init-config        - 创建默认配置文件"
    echo ""
    echo -e "${YELLOW}示例:${NC}"
    echo "  ./git-manager.sh status"
    echo "  ./git-manager.sh quick \"修复bug\""
    echo "  ./git-manager.sh new-branch feature/new-feature"
    echo "  ./git-manager.sh release v1.0.0"
}

# 检查是否在git仓库中
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        echo -e "${RED}错误: 当前目录不是Git仓库${NC}"
        exit 1
    fi
}

# 获取当前分支名
get_current_branch() {
    git branch --show-current
}

# 显示配置信息
show_config() {
    echo -e "${CYAN}=== Git Manager 配置 ===${NC}"
    echo -e "${YELLOW}默认分支:${NC} ${DEFAULT_BRANCH:-main}"
    echo -e "${YELLOW}默认日志条数:${NC} ${DEFAULT_LOG_COUNT:-10}"
    echo -e "${YELLOW}自动推送:${NC} ${AUTO_PUSH:-true}"
    echo -e "${YELLOW}使用颜色:${NC} ${USE_COLORS:-true}"
    echo -e "${YELLOW}详细输出:${NC} ${VERBOSE:-true}"
    echo -e "${YELLOW}显示时间戳:${NC} ${SHOW_TIMESTAMPS:-false}"
    echo -e "${YELLOW}危险操作确认:${NC} ${CONFIRM_DANGEROUS_OPERATIONS:-true}"
    echo -e "${YELLOW}远程仓库名:${NC} ${REMOTE_NAME:-origin}"
}

# 初始化配置文件
init_config() {
    local config_file=".git-manager.config"
    if [ -f "$config_file" ]; then
        echo -e "${YELLOW}配置文件已存在，是否覆盖? (y/N):${NC}"
        read -r response
        case "$response" in
            [yY][eE][sS]|[yY])
                ;;
            *)
                echo -e "${RED}操作已取消${NC}"
                exit 0
                ;;
        esac
    fi

    cp .git-manager.config.template "$config_file" 2>/dev/null || {
        echo -e "${YELLOW}创建默认配置文件...${NC}"
        cat > "$config_file" << 'EOF'
# Git Manager 配置文件
DEFAULT_LOG_COUNT=10
DEFAULT_BRANCH="main"
AUTO_PUSH=true
USE_COLORS=true
VERBOSE=true
SHOW_TIMESTAMPS=false
CONFIRM_DANGEROUS_OPERATIONS=true
REMOTE_NAME="origin"
EOF
    }
    echo -e "${GREEN}配置文件已创建: $config_file${NC}"
}

# 显示状态信息
show_status() {
    show_timestamp "${CYAN}=== Git仓库状态 ===${NC}"
    git status --short --branch
    echo ""
    echo -e "${YELLOW}未暂存的文件:${NC}"
    git diff --name-only
    echo ""
    echo -e "${YELLOW}已暂存的文件:${NC}"
    git diff --cached --name-only
}

# 添加文件
add_files() {
    if [ -z "$1" ]; then
        echo -e "${YELLOW}添加所有文件到暂存区...${NC}"
        git add .
    else
        echo -e "${YELLOW}添加文件: $1${NC}"
        git add "$1"
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}文件添加成功${NC}"
    else
        echo -e "${RED}文件添加失败${NC}"
        exit 1
    fi
}

# 提交变更
commit_changes() {
    local message="$1"
    if [ -z "$message" ]; then
        echo -e "${YELLOW}请输入提交消息:${NC}"
        read -r message
    fi
    
    echo -e "${YELLOW}提交变更: $message${NC}"
    git commit -m "$message"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}提交成功${NC}"
    else
        echo -e "${RED}提交失败${NC}"
        exit 1
    fi
}

# 推送到远程仓库
push_changes() {
    local branch="${1:-$(get_current_branch)}"
    echo -e "${YELLOW}推送到远程分支: $branch${NC}"
    git push origin "$branch"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}推送成功${NC}"
    else
        echo -e "${RED}推送失败${NC}"
        exit 1
    fi
}

# 从远程仓库拉取
pull_changes() {
    local branch="${1:-$(get_current_branch)}"
    echo -e "${YELLOW}从远程分支拉取: $branch${NC}"
    git pull origin "$branch"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}拉取成功${NC}"
    else
        echo -e "${RED}拉取失败${NC}"
        exit 1
    fi
}

# 同步操作
sync_repo() {
    echo -e "${CYAN}=== 开始同步仓库 ===${NC}"
    
    # 1. 拉取最新变更
    echo -e "${YELLOW}步骤1: 拉取远程变更...${NC}"
    pull_changes
    
    # 2. 添加所有文件
    echo -e "${YELLOW}步骤2: 添加文件到暂存区...${NC}"
    add_files
    
    # 3. 检查是否有变更需要提交
    if git diff --cached --quiet; then
        echo -e "${GREEN}没有变更需要提交${NC}"
        return 0
    fi
    
    # 4. 提交变更
    echo -e "${YELLOW}步骤3: 提交变更...${NC}"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    commit_changes "自动同步 - $timestamp"
    
    # 5. 推送变更
    echo -e "${YELLOW}步骤4: 推送变更...${NC}"
    push_changes
    
    echo -e "${GREEN}=== 同步完成 ===${NC}"
}

# 快速提交推送
quick_commit() {
    local message="$1"
    if [ -z "$message" ]; then
        echo -e "${YELLOW}请输入提交消息:${NC}"
        read -r message
    fi
    
    echo -e "${CYAN}=== 快速提交推送 ===${NC}"
    add_files
    commit_changes "$message"
    push_changes
    echo -e "${GREEN}=== 快速操作完成 ===${NC}"
}

# 创建新分支
create_branch() {
    local branch_name="$1"
    if [ -z "$branch_name" ]; then
        echo -e "${YELLOW}请输入分支名称:${NC}"
        read -r branch_name
    fi
    
    echo -e "${YELLOW}创建并切换到分支: $branch_name${NC}"
    git checkout -b "$branch_name"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}分支创建成功${NC}"
    else
        echo -e "${RED}分支创建失败${NC}"
        exit 1
    fi
}

# 删除分支
delete_branch() {
    local branch_name="$1"
    if [ -z "$branch_name" ]; then
        echo -e "${RED}请指定要删除的分支名称${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}删除分支: $branch_name${NC}"
    git branch -d "$branch_name"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}分支删除成功${NC}"
    else
        echo -e "${RED}分支删除失败，尝试强制删除...${NC}"
        git branch -D "$branch_name"
    fi
}

# 创建备份分支
create_backup() {
    local current_branch=$(get_current_branch)
    local backup_name="backup-${current_branch}-$(date '+%Y%m%d-%H%M%S')"
    
    echo -e "${YELLOW}创建备份分支: $backup_name${NC}"
    git checkout -b "$backup_name"
    git checkout "$current_branch"
    
    echo -e "${GREEN}备份分支创建完成: $backup_name${NC}"
}

# 创建发布标签
create_release() {
    local version="$1"
    if [ -z "$version" ]; then
        echo -e "${YELLOW}请输入版本号:${NC}"
        read -r version
    fi
    
    echo -e "${YELLOW}创建发布标签: $version${NC}"
    git tag -a "$version" -m "Release $version"
    git push origin "$version"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}发布标签创建成功${NC}"
    else
        echo -e "${RED}发布标签创建失败${NC}"
        exit 1
    fi
}

# 主函数
main() {
    # 加载配置
    load_config

    # 检查参数
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi

    # 检查git仓库（除了clone、help和config操作）
    case "$1" in
        "help"|"h"|"clone"|"config"|"init-config")
            ;;
        *)
            check_git_repo
            ;;
    esac
    
    # 执行操作
    case "$1" in
        "help"|"h")
            show_help
            ;;
        "status"|"s")
            show_status
            ;;
        "add")
            add_files "$2"
            ;;
        "commit"|"c")
            commit_changes "$2"
            ;;
        "push"|"p")
            push_changes "$2"
            ;;
        "pull")
            pull_changes "$2"
            ;;
        "sync")
            sync_repo
            ;;
        "quick"|"q")
            quick_commit "$2"
            ;;
        "branch"|"b")
            git branch -a
            ;;
        "checkout"|"co")
            git checkout "$2"
            ;;
        "new-branch"|"nb")
            create_branch "$2"
            ;;
        "delete-branch"|"db")
            delete_branch "$2"
            ;;
        "merge")
            git merge "$2"
            ;;
        "log"|"l")
            local count="${2:-$DEFAULT_LOG_COUNT}"
            if [ "$SHOW_BRANCH_GRAPH" = true ]; then
                git log --oneline --graph -n "$count"
            else
                git log --oneline -n "$count"
            fi
            ;;
        "diff")
            git diff "$2"
            ;;
        "show")
            git show "$2"
            ;;
        "remote")
            git remote -v
            ;;
        "clone")
            git clone "$2" "$3"
            ;;
        "fetch")
            git fetch
            ;;
        "tag")
            if [ -n "$2" ]; then
                git tag -a "$2" -m "${3:-Tag $2}"
            else
                git tag
            fi
            ;;
        "tags")
            git tag
            ;;
        "push-tags")
            git push --tags
            ;;
        "reset")
            git reset "$2"
            ;;
        "revert")
            git revert "$2"
            ;;
        "clean")
            git clean -fd
            ;;
        "backup")
            create_backup
            ;;
        "release")
            create_release "$2"
            ;;
        "config")
            show_config
            ;;
        "init-config")
            init_config
            ;;
        *)
            echo -e "${RED}未知操作: $1${NC}"
            echo -e "${YELLOW}使用 './git-manager.sh help' 查看帮助${NC}"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
