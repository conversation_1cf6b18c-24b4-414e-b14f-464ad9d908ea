# VPP-AI 项目 .gitignore 文件

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# VPP-AI 项目特定文件
# 数据文件
data/raw/
data/processed/
data/models/*.pkl
data/models/*.h5
data/models/*.joblib

# 日志文件
logs/
*.log

# 配置文件（包含敏感信息）
config/local.yaml
config/production.yaml
.env.local
.env.production

# 临时文件
temp/
tmp/
*.tmp

# 图片和可视化输出
*.png
*.jpg
*.jpeg
*.gif
*.svg
plots/
charts/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# API密钥和凭证
api_keys.txt
credentials.json
secrets.yaml

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# 模型文件（大文件）
*.model
*.weights
*.checkpoint

# 缓存文件
cache/
.cache/

# 测试输出
test_output/
test_results/

# 文档生成
docs/build/
docs/_build/

# 部署相关
docker-compose.override.yml
.dockerignore

# 备份文件
*.bak
*.backup

# 性能分析文件
*.prof

# 其他
.pytest_cache/
.coverage
htmlcov/
